//
//  WebViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/4/17.
//
//  H5展示页

import UIKit
import WebKit
import CoreLocation
// 请确保您已正确导入 WebViewJavascriptBridge 库，例如:

class WebViewController: BaseViewController, CLLocationManagerDelegate {
    // MARK: - Properties
    private let url: URL
    private var titleText: String?
    private var shouldUseWebPageTitle: Bool = false
    // 是否强制显示导航栏，外部可控制
    private var forceShowNavBar: Bool = false
    // 控制是否显示自定义导航栏（路径初始化的页面不显示）
    private var shouldHideNavigationBar: Bool = false
    // 标记是否通过 path 方式初始化
    private var isPathInitialization: Bool = false
    // 路径初始化且非特殊路径时，页面加载成功后需要隐藏导航栏
    var hideNavBarAfterLoad: Bool = false
    private var bridge: WebViewJavascriptBridge! // 新增: JSBridge 实例
    
    // 定位相关
    private var locationManager: CLLocationManager?
    private var locationCallbackId: String? // 存储定位回调ID
    private var locationResponseCallback: (([String: Any]) -> Void)? // 存储定位响应回调

    // 支付相关
    private var isPaying: Bool = false
    private var payType: String = ""
    private let wxPayType = "APPWX"
    private let aliPayType = "APPZFB"

    // 微信小程序相关
    private let wxMiniProgramId = "gh_f8187e427899" // 微信小程序原始ID
    // 注意：这里应该使用与Android相同的H5UrlConstants.CASHIER_PAGE值
    // 暂时使用通用的收银台路径，实际应该从配置中获取
    private let cashierPagePath = "newsPage/cashierPage/cashierPage" // 收银台页面路径
    
    // MARK: - UI Components
    private lazy var webView: WKWebView = {
        let config = WKWebViewConfiguration()
        let webView = WKWebView(frame: .zero, configuration: config)
        webView.navigationDelegate = self
        // 添加 URL 变化的观察者
        webView.addObserver(self, forKeyPath: #keyPath(WKWebView.url), options: .new, context: nil)
        webView.translatesAutoresizingMaskIntoConstraints = false
        return webView
    }()
    
    private lazy var loadingIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.hidesWhenStopped = true
        indicator.translatesAutoresizingMaskIntoConstraints = false
        return indicator
    }()
    
    // MARK: - Initialization
    init(url: URL, title: String? = nil) {
        self.url = url
        self.titleText = title
        self.shouldUseWebPageTitle = (title == nil)
        // 直接传入完整 URL 时，默认显示导航栏
        self.shouldHideNavigationBar = false
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - H5 Base Domain 配置
    /// 默认的 H5 域名，可按需在 App 启动时动态修改。
    static var baseH5Domain: String = {
//        return "http://192.168.10.103:8080/#/"
        return "https://yigou.gzyoushu.com/#/"
    }()
    
    // 特殊路径列表：这些路径即使是用path初始化，也需要显示导航栏
    private static var specialPathsNeedingNavBar: [String] = [
        // 根据用户日志添加不需要原生导航栏的路径（首页需要导航栏）
        "/pages/news/news",
        "/pages/live/live",
        "/pages/goodCart/goodCart",
        "/pages/user/user",
//        "sxqVideo/shoppingIndex/shoppingIndex",
        "" // 空路径（商城首页）也需要显示导航栏
    ]

    // MARK: - Convenience Initializer for H5 Path (仅传入路径)
    /// 仅需传入 H5 页面路径，域名由 `baseH5Domain` 统一配置。
    /// - Parameters:
    ///   - path: H5 页面路径，例如 "/pages/order/index"。
    ///   - title: 可选页面标题。
    convenience init(path: String, title: String? = nil) {
        var normalizedPath = path
        // 移除自动添加的斜杠，因为域名已经包含了 "#/"
        if normalizedPath.hasPrefix("/") { normalizedPath = String(normalizedPath.dropFirst()) }

        let fullURLString = WebViewController.baseH5Domain + normalizedPath
        guard let completeURL = URL(string: fullURLString) else {
            fatalError("无法构建有效的 URL：\(fullURLString)")
        }
        self.init(url: completeURL, title: title)
        
        // 路径初始化默认先显示导航栏，防止加载失败无法返回
        self.shouldHideNavigationBar = false
        self.isPathInitialization = true
        
        // 检查是否是特殊路径
        let isSpecialPath = WebViewController.specialPathsNeedingNavBar.contains { specialPath in
            normalizedPath == specialPath || normalizedPath.hasPrefix(specialPath + "/")
        }
        
        // 非特殊路径，加载成功后需要隐藏导航栏
        self.hideNavBarAfterLoad = !isSpecialPath
    }
    
    // MARK: - 外部控制导航栏
    
    /// 使用此方法强制显示导航栏，不管URL是什么
    /// - Parameter show: 是否显示导航栏
    func forceNavigationBarVisibility(show: Bool) {
        self.forceShowNavBar = show
        self.shouldHideNavigationBar = !show
        updateNavigationBarVisibility()
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()

        // 初始根据 shouldHideNavigationBar 设置 BaseViewController 导航栏显示
        showNavBar = !shouldHideNavigationBar

        // 设置标题
        if let title = titleText {
            navTitle = title
        } else {
            // 尝试从 URL 提取页面类型作为标题
            let extractedTitle = extractTitleFromURL(url)
            navTitle = extractedTitle ?? "加载中..."
        }

        // 如果是作为子控制器嵌套在其他控制器中，不应该影响TabBar的显示
        // 检查是否有父控制器且父控制器不是导航控制器
        if parent != nil && !(parent is UINavigationController) {
            // 作为子控制器时，不应该被标记为TabBar根视图控制器
            isTabBarRootViewController = false
            print("WebViewController: 作为子控制器，不影响TabBar显示")
        }

        // 设置内容视图
        // 监听登录成功后的 Token 保存通知
        NotificationCenter.default.addObserver(self, selector: #selector(handleTokenSaved(_:)), name: .tokenSaved, object: nil)

        setupUI()
        setupJavascriptBridge() // 新增: 初始化JSBridge
        setupURLMonitoring()
        loadWebContent()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)

        // 如果是作为子控制器，确保不干扰父控制器的TabBar显示逻辑
        if parent != nil && !(parent is UINavigationController) {
            print("WebViewController viewWillAppear: 作为子控制器，跳过TabBar更新")
            return
        }
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // 如果是作为子控制器，确保不干扰父控制器的TabBar显示逻辑
        if parent != nil && !(parent is UINavigationController) {
            print("WebViewController viewDidAppear: 作为子控制器，跳过TabBar更新")
            return
        }

        // 处理支付状态恢复
        handlePaymentResume()

        // 添加微信小程序支付结果监听
        setupWeChatMiniProgramPaymentObserver()
    }

    /// 设置微信小程序支付结果监听
    private func setupWeChatMiniProgramPaymentObserver() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleWeChatMiniProgramPaymentResult(_:)),
            name: .wxMiniProgramPaymentResult,
            object: nil
        )
    }



    /// 处理微信小程序支付结果
    @objc private func handleWeChatMiniProgramPaymentResult(_ notification: Notification) {
        guard let userInfo = notification.userInfo,
              let paymentResult = userInfo["paymentResult"] as? String else {
            print("【微信支付】无效的支付结果数据")
            return
        }

        print("【微信支付】收到小程序支付结果: \(paymentResult)")

        // 解析支付结果
        parseAndNotifyPaymentResult(paymentResult)
    }

    /// 解析并通知支付结果
    private func parseAndNotifyPaymentResult(_ resultString: String) {
        // 尝试解析JSON格式的支付结果
        if let data = resultString.data(using: .utf8) {
            do {
                if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any],
                   let status = json["status"] as? Int {
                    print("【微信支付】解析到支付状态: \(status)")
                    notifyPaymentResult(status: status)
                    return
                }
            } catch {
                print("【微信支付】JSON解析失败: \(error)")
            }
        }

        // 如果JSON解析失败，默认返回未知状态
        print("【微信支付】无法解析支付结果，返回未知状态")
        notifyPaymentResult(status: 3)
    }



    /// 处理支付状态恢复（从其他应用返回时）
    private func handlePaymentResume() {
        if isPaying {
            print("【支付恢复】检测到支付状态，支付类型: \(payType)")

            if payType == wxPayType {
                // 微信支付：默认返回未知状态，实际状态通过微信SDK回调获取
                print("【支付恢复】微信支付恢复，状态未知")
                notifyPaymentResult(status: 3) // 3: 状态未知
            } else if payType == aliPayType {
                // 支付宝支付：已经在跳转Safari时返回了成功状态，这里不需要额外处理
                print("【支付恢复】支付宝支付恢复，已处理完成")
                // 重置支付状态
                isPaying = false
                payType = ""
            } else {
                print("【支付恢复】未知支付类型")
                notifyPaymentResult(status: 3) // 3: 状态未知
            }
        }
    }



    // 重写TabBar可见性更新方法，确保作为子控制器时不影响TabBar
    override func updateTabBarVisibility() {
        // 如果是作为子控制器嵌套在其他控制器中，不应该影响TabBar的显示
        if parent != nil && !(parent is UINavigationController) {
            print("WebViewController updateTabBarVisibility: 作为子控制器，跳过TabBar可见性更新")
            return
        }

        // 否则调用父类的方法
        super.updateTabBarVisibility()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // 添加 WebView 到内容视图
        contentView.addSubview(webView)
        view.addSubview(loadingIndicator)
        
        // 让 WebView 填充整个内容视图
        webView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            webView.topAnchor.constraint(equalTo: contentView.topAnchor),
            webView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            webView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            webView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            
            loadingIndicator.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            loadingIndicator.centerYAnchor.constraint(equalTo: view.centerYAnchor),
        ])
    }
    
    // MARK: - JS Bridge Setup (新增部分)
    private func setupJavascriptBridge() {
        WebViewJavascriptBridge.enableLogging()
        
        // 创建桥接实例
        bridge = WebViewJavascriptBridge(forWebView: webView)
        bridge.setWebViewDelegate(self)
        
        print("创建的Bridge类型: \(type(of: bridge))")
        
        // 注册一个供H5调用的handler示例： "nativeAction"
        bridge.registerHandler("nativeAction") { [weak self] (data, responseCallback) in
            print("nativeAction called by JS with data: \(data ?? "nil")")
            let responseData = ["message": "Response from Swift: nativeAction executed", "status": 1]
            self?.sendJSONResponse(responseCallback: responseCallback, data: responseData, handlerName: "nativeAction")
        }

        // 注册demo中的 "changeUser" handler
        bridge.registerHandler("changeUser") { [weak self] (data, responseCallback) in
            if let userName = data as? String {
                print("changeUser called from JS with userName: \(userName)")
                self?.titleLabel.text = "User: \(userName)"
            }
            let responseData = ["message": "User changed successfully by Swift", "status": 1]
            self?.sendJSONResponse(responseCallback: responseCallback, data: responseData, handlerName: "changeUser")
        }
        
        // 注册拨打电话的handler
        bridge.registerHandler("goCallPhone") { [weak self] (data, responseCallback) in
            guard let self = self else { return }
            
            // 打印原始数据，帮助调试
            print("goCallPhone接收到的原始数据: \(data ?? "nil")")
            
            // 获取电话号码
            var phoneNumber: String = ""
            
            // 数据可能直接是字典
            if let dataDict = data as? [String: Any] {
                if let phone = dataDict["phone"] {
                    // 直接在第一层找到phone
                    phoneNumber = "\(phone)"
                    print("直接获取到电话号码: \(phoneNumber)")
                } else if let nestedData = dataDict["data"] as? [String: Any], let phone = nestedData["phone"] {
                    // 在嵌套的data字段中找到phone
                    phoneNumber = "\(phone)"
                    print("从嵌套data中获取到电话号码: \(phoneNumber)")
                }
            }
            
            // 如果还是没获取到，尝试将整个数据转为字符串查看
            if phoneNumber.isEmpty {
                print("未能解析电话号码，原始数据结构: \(String(describing: data))")
                
                // 尝试其他可能的数据格式
                if let phoneString = data as? String, !phoneString.isEmpty {
                    phoneNumber = phoneString
                    print("从字符串中获取到电话号码: \(phoneNumber)")
                }
            }
            
            // 确保电话号码有效
            if phoneNumber.isEmpty {
                print("最终未获取到电话号码")
                // 使用JSON格式返回失败响应
                let errorData = ["data": NSNull(), "status": 0, "msg": "未提供电话号码或格式不正确"] as [String : Any]
                self.sendJSONResponse(responseCallback: responseCallback, data: errorData, handlerName: "goCallPhone")
                return
            }
            
            // 拨打电话
            if let url = URL(string: "tel://\(phoneNumber)") {
                DispatchQueue.main.async {
                    if UIApplication.shared.canOpenURL(url) {
                        UIApplication.shared.open(url, options: [:]) { success in
                            if success {
                                print("成功显示拨打电话界面: \(phoneNumber)")
                                // 使用JSON格式返回成功响应
                                let resultData: [String: Any] = ["phone": phoneNumber]
                                let successData = ["data": resultData, "status": 1, "msg": "已显示拨打电话界面"] as [String : Any]
                                self.sendJSONResponse(responseCallback: responseCallback, data: successData, handlerName: "goCallPhone")
                            } else {
                                print("无法显示拨打电话界面")
                                // 使用JSON格式返回失败响应
                                let errorData = ["data": NSNull(), "status": 0, "msg": "无法显示拨打电话界面"] as [String : Any]
                                self.sendJSONResponse(responseCallback: responseCallback, data: errorData, handlerName: "goCallPhone")
                            }
                        }
                    } else {
                        print("设备不支持拨打电话功能")
                        self.showAlert(message: "无法拨打电话")
                        // 使用JSON格式返回失败响应
                        let errorData = ["data": NSNull(), "status": 0, "msg": "设备不支持拨打电话功能"] as [String : Any]
                        self.sendJSONResponse(responseCallback: responseCallback, data: errorData, handlerName: "goCallPhone")
                    }
                }
            } else {
                // 使用JSON格式返回失败响应
                let errorData = ["data": NSNull(), "status": 0, "msg": "无效的电话号码格式"] as [String : Any]
                self.sendJSONResponse(responseCallback: responseCallback, data: errorData, handlerName: "goCallPhone")
            }
        }
        
        // 注册获取用户 Token 的 handler（常规 WVJB 回调）
        bridge.registerHandler("goGetToken") { [weak self] (data, responseCallback) in
            guard let self = self else { return }
            
            // 使用新方法发送 token 响应
            self.sendTokenResponse(responseCallback: responseCallback)
            
            // 打印调试信息
            print("goGetToken 已处理，返回简化 token 格式")
        }
        
        // 注册 goLogin 处理程序
        bridge.registerHandler("goLogin") { [weak self] (data, responseCallback) in
            guard let self = self else { return }

            print("[WebViewController] goLogin called, presenting native login page…")

            // 跳转到原生登录页面
            let loginVC = LoginViewController2_0()
            loginVC.modalPresentationStyle = .fullScreen
            self.present(loginVC, animated: true, completion: nil)

            // 登录完成后 token 将通过 Notification .tokenSaved 回传，由 handleTokenSaved 统一处理
        }
        
        // 注册获取位置的handler
        bridge.registerHandler("goLocation") { [weak self] (data, responseCallback) in
            guard let self = self else { return }

            print("【注册处理】goLocation接收到的原始数据: \(data ?? "nil")")

            // 保存回调函数
            self.locationCallbackId = nil // 清除之前的回调ID

            // 创建一个自定义回调函数，用于通过responseCallback返回JSON结果
            let locationResponseCallback: (([String: Any]) -> Void)? = { responseData in
                print("【注册处理】goLocation返回数据: \(responseData)")
                // 使用统一的JSON响应方法
                self.sendJSONResponse(responseCallback: responseCallback, data: responseData, handlerName: "goLocation")
            }

            // 初始化定位管理器
            if self.locationManager == nil {
                self.locationManager = CLLocationManager()
                self.locationManager?.delegate = self
                self.locationManager?.desiredAccuracy = kCLLocationAccuracyBest
                print("【注册处理】初始化定位管理器")
            } else {
                print("【注册处理】使用现有定位管理器")
            }

            // 检查定位权限
            let authStatus = CLLocationManager.authorizationStatus()
            if authStatus == .notDetermined {
                // 请求定位权限
                print("【注册处理】请求定位权限")
                self.locationManager?.requestWhenInUseAuthorization()
                // 保存回调以便权限授予后使用
                self.locationResponseCallback = locationResponseCallback
            } else if authStatus == .denied || authStatus == .restricted {
                // 用户拒绝定位权限
                print("【注册处理】定位权限被拒绝")
                self.showAlert(message: "请在设置中开启定位权限")
                locationResponseCallback?(["data": NSNull(), "status": 0, "msg": "定位权限被拒绝"])
            } else {
                // 保存回调
                print("【注册处理】已获得定位权限，开始定位")
                self.locationResponseCallback = locationResponseCallback
                // 开始定位
                self.locationManager?.startUpdatingLocation()
            }
        }

        // 注册支付handler
        bridge.registerHandler("goPos") { [weak self] (data, responseCallback) in
            guard let self = self else { return }

            print("【支付处理】goPos接收到的原始数据: \(data ?? "nil")")

            // 解析支付数据
            guard let dataDict = data as? [String: Any] else {
                print("【支付处理】数据格式错误")
                responseCallback?(["data": NSNull(), "status": 0, "msg": "支付数据格式错误"])
                return
            }

            // 解析订单详情
            guard let orderDetail = dataDict["orderDetail"] as? [String: Any],
                  let payType = orderDetail["type"] as? String else {
                print("【支付处理】缺少订单详情或支付类型")
                responseCallback?(["data": NSNull(), "status": 0, "msg": "缺少订单详情或支付类型"])
                return
            }

            print("【支付处理】支付类型: \(payType)")

            // 先返回成功响应给H5
            responseCallback?(["data": NSNull(), "status": 1, "msg": "支付请求已接收"])

            // 根据支付类型处理
            if payType == "APPWX" {
                // 微信支付
                self.handleWechatPay(payData: dataDict)
            } else if payType == "APPZFB" {
                // 支付宝支付
                self.handleAlipay(payData: dataDict)
            } else {
                print("【支付处理】不支持的支付类型: \(payType)")
                self.showAlert(message: "支付类型暂不支持")
                // 通知H5支付失败
                self.notifyPaymentResult(status: 0) // 0: 支付失败
            }
        }

        // 注册回到首页handler
        bridge.registerHandler("goHome") { [weak self] (data, responseCallback) in
            guard let self = self else { return }

            print("【回到首页】goHome被调用")

            // 先返回JSON格式成功响应给H5
            let responseData = ["data": NSNull(), "status": 1, "msg": "正在返回首页"] as [String : Any]
            self.sendJSONResponse(responseCallback: responseCallback, data: responseData, handlerName: "goHome")

            // 回到首页
            self.navigateToHome()
        }

        // 注册支付结果接收handler（供H5主动通知支付结果使用）
        bridge.registerHandler("onPaymentResult") { [weak self] (data, responseCallback) in
            guard let self = self else { return }

            print("【支付结果】H5主动通知支付结果: \(data ?? "nil")")

            // 返回JSON格式确认响应给H5
            let responseData = ["status": 3, "msg": "支付结果已接收"]
            self.sendJSONResponse(responseCallback: responseCallback, data: responseData, handlerName: "onPaymentResult")

            // 这里可以根据需要处理H5传递的支付结果
            // 例如更新UI、显示提示等
        }

        // 注册地图导航handler
        bridge.registerHandler("goMapPath") { [weak self] (data, responseCallback) in
            guard let self = self else { return }

            print("【地图导航】goMapPath接收到的原始数据: \(data ?? "nil")")

            // 解析导航数据
            guard let dataDict = data as? [String: Any] else {
                print("【地图导航】数据格式错误")
                responseCallback?(["data": NSNull(), "status": 0, "msg": "导航数据格式错误"])
                return
            }

            let lat = dataDict["lat"] as? String ?? ""
            let lng = dataDict["lng"] as? String ?? ""
            let name = dataDict["name"] as? String ?? ""

            print("【地图导航】纬度: \(lat), 经度: \(lng), 名称: \(name)")

            // 先返回成功响应给H5
            responseCallback?(["data": NSNull(), "status": 1, "msg": "正在打开地图导航"])

            // 打开地图导航
            self.openMapNavigation(latitude: lat, longitude: lng, name: name)
        }

        // 注册分享handler
        bridge.registerHandler("goOpenShare") { [weak self] (data, responseCallback) in
            guard let self = self else { return }

            print("【分享功能】goOpenShare接收到的原始数据: \(data ?? "nil")")

            // 解析分享数据
            guard let dataDict = data as? [String: Any] else {
                print("【分享功能】数据格式错误")
                responseCallback?(["data": NSNull(), "status": 0, "msg": "分享数据格式错误"])
                return
            }

            // 先返回成功响应给H5
            responseCallback?(["data": NSNull(), "status": 3, "msg": "正在打开分享"])

            // 处理分享
            self.handleShare(shareData: dataDict)
        }

        // 注册返回上一页handler
        bridge.registerHandler("goBackPrevPage") { [weak self] (data, responseCallback) in
            guard let self = self else { return }

            print("【返回上一页】goBackPrevPage被调用")

            // 先返回成功响应给H5
            responseCallback?(["data": NSNull(), "status": 1, "msg": "正在返回上一页"])

            // 返回上一页
            self.goBackPrevPage()
        }

        // 设置处理未捕获消息的回调
        bridge.setUnhandledMessageCallback { [weak self] (bridge, message) in
            guard let self = self else { return }
            
            // 打印未捕获的消息
            if let messageDict = message as? [String: Any] {
                print("【WebViewController】未处理的JS消息: \(messageDict)")
                
                // 获取处理程序名称
                if let handlerName = messageDict["handlerName"] as? String {
                    print("【WebViewController】JS尝试调用未注册的处理程序: \(handlerName)")
                    
                    // 获取数据
                    if let data = messageDict["data"] {
                        print("【WebViewController】JS传递的数据: \(data)")
                        
                        // 在这里可以根据handlerName进行自定义处理
                        self.handleUnregisteredJSHandler(handlerName: handlerName, data: message ?? "")
                    }
                    
                    // 如果有回调ID，可以向JS返回一个错误消息
                    if let callbackId = messageDict["callbackId"] as? String {
                        // 使用标准格式返回失败响应
                        self.sendFailureToJS(callbackId: callbackId, msg: "未找到处理程序: \(handlerName)")
                    }
                }
            }
        }
    }
    
    // 处理未注册的JS处理程序
    private func handleUnregisteredJSHandler(handlerName: String, data: Any) {
        // 这里可以根据handlerName来决定如何处理未注册的JS调用
        switch handlerName {
        case "goScanCode":
            //打开扫码页面
            let scanVC = QRScanViewController()
            self.navigationController?.pushViewController(scanVC, animated: true)
        case "goOpenShare":
            print("打开分享页面")
            
        case "goCallPhone":
            // 打印原始数据，帮助调试
            print("未注册处理程序捕获goCallPhone，原始数据: \(data)")
            
            // 获取电话号码和回调ID
            var phoneNumber: String = ""
            var callbackId: String? = nil
            
            if let messageDict = data as? [String: Any] {
                // 获取回调ID
                if let cbId = messageDict["callbackId"] as? String {
                    callbackId = cbId
                }
                
                // 尝试获取数据中的电话号码 - 可能在data字段嵌套
                if let jsonData = messageDict["data"] as? [String: Any] {
                    if let phone = jsonData["phone"] {
                        // 将phone转换为字符串
                        phoneNumber = "\(phone)"
                        print("从嵌套data中获取到电话号码: \(phoneNumber)")
                    }
                }
                
                // 如果在data中没找到，尝试直接在第一层找
                if phoneNumber.isEmpty, let phone = messageDict["phone"] {
                    phoneNumber = "\(phone)"
                    print("直接获取到电话号码: \(phoneNumber)")
                }
            }
            
            // 确保电话号码有效
            if phoneNumber.isEmpty {
                print("最终未获取到电话号码")
                if let callbackId = callbackId {
                    // 使用标准格式返回失败响应
                    sendFailureToJS(callbackId: callbackId, msg: "未提供电话号码或格式不正确")
                }
                return
            }
            
            // 拨打电话
            if let url = URL(string: "tel://\(phoneNumber)") {
                DispatchQueue.main.async {
                    if UIApplication.shared.canOpenURL(url) {
                        UIApplication.shared.open(url, options: [:]) { success in
                            if success {
                                print("成功显示拨打电话界面: \(phoneNumber)")
                                
                                // 如果有回调ID，返回显示结果
                                if let callbackId = callbackId {
                                    // 使用标准格式返回成功响应
                                    let resultData: [String: Any] = ["phone": phoneNumber]
                                    self.sendSuccessToJS(callbackId: callbackId, data: resultData, msg: "已显示拨打电话界面")
                                }
                            } else {
                                print("无法显示拨打电话界面")
                                
                                // 如果有回调ID，返回错误结果
                                if let callbackId = callbackId {
                                    // 使用标准格式返回失败响应
                                    self.sendFailureToJS(callbackId: callbackId, msg: "无法显示拨打电话界面")
                                }
                            }
                        }
                    } else {
                        print("设备不支持拨打电话功能")
                        // 可以显示一个提示
                        self.showAlert(message: "无法拨打电话")
                        
                        // 如果有回调ID，返回错误信息
                        if let callbackId = callbackId {
                            // 使用标准格式返回失败响应
                            self.sendFailureToJS(callbackId: callbackId, msg: "设备不支持拨打电话功能")
                        }
                    }
                }
            }
            
        case "getAppInfo":
            // 示例：如果JS尝试获取应用信息，我们可以在这里动态处理
            let appInfo = ["appName": "Shuxiaoqi", "version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? ""]
            print("应用信息已准备: \(appInfo)")
            // 如果需要，您可以将这些信息传回JS
            
        case "navigate":
            // 示例：处理导航请求
            if let navigationData = data as? [String: Any], let page = navigationData["page"] as? String {
                print("收到导航请求到: \(page)")
                // 在这里可以执行相应的导航逻辑
            }
            
        case "goBackPrevPage":
            self.dismiss(animated: true)
            self.navigationController?.popViewController(animated: true)
            
        case "goLogin":
            // 处理登录请求
            print("捕获到goLogin未注册处理")
            var callbackId: String? = nil
            
            // 获取回调ID
            if let messageDict = data as? [String: Any], let cbId = messageDict["callbackId"] as? String {
                callbackId = cbId
            }
            
            // 使用新方法发送 token 响应
            self.sendTokenResponse(responseCallback: { (response) in
                if let responseDict = response as? [String: Any], let status = responseDict["status"] as? Int {
                    if status == 1 {
                        self.sendSuccessToJS(callbackId: callbackId ?? "", data: responseDict["data"] as Any, msg: responseDict["msg"] as? String ?? "操作成功")
                    } else {
                        self.sendFailureToJS(callbackId: callbackId ?? "", msg: responseDict["msg"] as? String ?? "操作失败", data: responseDict["data"] as Any)
                    }
                }
            })
            
            // 打印调试信息
            print("goLogin 未注册处理已完成，返回简化 token 格式")
            
        default:
            print("未知的处理程序: \(handlerName)，暂不处理")
        }
    }
    
    // 向JS发送错误响应
    private func sendErrorResponseToJS(callbackId: String, data: Any) {
        // 创建响应消息
        let responseMessage: [String: Any] = [
            "responseId": callbackId,
            "responseData": data
        ]
        
        // 将消息序列化为JSON字符串
        if let jsonData = try? JSONSerialization.data(withJSONObject: responseMessage, options: []),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            // 构建JavaScript命令
            let javascriptCommand = "WebViewJavascriptBridge._handleMessageFromObjC('\(jsonString.replacingOccurrences(of: "'", with: "\\'"))');"
            // 在主线程上执行JavaScript
            DispatchQueue.main.async {
                self.webView.evaluateJavaScript(javascriptCommand, completionHandler: { (result, error) in
                    if let error = error {
                        print("发送错误响应到JS失败: \(error)")
                    }
                })
            }
        }
    }
    
    // 向JS发送成功响应
    private func sendSuccessResponseToJS(callbackId: String, data: Any) {
        // 创建响应消息
        let responseMessage: [String: Any] = [
            "responseId": callbackId,
            "responseData": data
        ]
        
        // 将消息序列化为JSON字符串
        if let jsonData = try? JSONSerialization.data(withJSONObject: responseMessage, options: []),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            // 构建JavaScript命令
            let javascriptCommand = "WebViewJavascriptBridge._handleMessageFromObjC('\(jsonString.replacingOccurrences(of: "'", with: "\\'"))');"
            // 在主线程上执行JavaScript
            DispatchQueue.main.async {
                self.webView.evaluateJavaScript(javascriptCommand, completionHandler: { (result, error) in
                    if let error = error {
                        print("发送成功响应到JS失败: \(error)")
                    }
                })
            }
        }
    }
    
    // 示例：调用H5的JS方法 (新增部分)
    // 您可以在需要的时候调用此方法，例如用户点击某个按钮
    func sendMessageToJavaScript(handlerName: String, data: Any?) {
        // 如果data是字典，转换为JSON字符串发送
        if let dataDict = data as? [String: Any] {
            sendJSONToBridge(handlerName: handlerName, data: dataDict)
        } else if let dataString = data as? String {
            // 如果已经是字符串，直接发送
            bridge.callHandler(handlerName, data: dataString)
            print("【桥接】发送字符串到H5 [\(handlerName)]: \(dataString)")
        } else if data == nil {
            // 如果是nil，发送空JSON对象
            sendJSONToBridge(handlerName: handlerName, data: [:])
        } else {
            print("【桥接】不支持的数据类型: \(type(of: data))")
        }
    }
    
    func flushMessageQueue() {
//        bridge.flatMap { responseData in
//            printf(responseData)
//        }
    }
    
    // 示例：调用demo中的 "changeName"
    func callChangeNameInJS(name: String) {
        sendMessageToJavaScript(handlerName: "changeName", data: name)
    }
    
    private func loadWebContent() {
        loadingIndicator.startAnimating()
        
        // 获取公参
        let commonParams = UserDefaults.standard.string(forKey: "commonParams") ?? ""
        
        // 构建URL请求
        if var urlComponents = URLComponents(url: url, resolvingAgainstBaseURL: false) {
            var queryItems = urlComponents.queryItems ?? []
            
            // 添加Token
            if let token = UserDefaults.standard.string(forKey: "userToken") {
                queryItems.append(URLQueryItem(name: "token", value: token))
            }
            
            // 添加公参
            if !commonParams.isEmpty {
                let commonParamsItems = commonParams.components(separatedBy: "&")
                for param in commonParamsItems {
                    let components = param.components(separatedBy: "=")
                    if components.count == 2 {
                        queryItems.append(URLQueryItem(name: components[0], value: components[1]))
                    }
                }
            }
            
            urlComponents.queryItems = queryItems
            
            if let updatedURL = urlComponents.url {
                let request = URLRequest(url: updatedURL)
                webView.load(request)
                return
            }
        }
        
        // 如果URL构建失败，使用原始URL
        let request = URLRequest(url: url)
        webView.load(request)
    }
    
    // 向JS发送标准格式的响应
    private func sendStandardResponseToJS(callbackId: String, data: Any? = nil, status: Int = 1, msg: String = "") {
        // 创建符合规范的响应数据
        let standardData: [String: Any] = [
            "data": data ?? NSNull(),
            "status": status,  // 0: 调用失败, 1: 调用成功
            "msg": msg
        ]
        
        // 创建响应消息
        let responseMessage: [String: Any] = [
            "responseId": callbackId,
            "responseData": standardData
        ]
        
        // 将消息序列化为JSON字符串
        if let jsonData = try? JSONSerialization.data(withJSONObject: responseMessage, options: []),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            // 构建JavaScript命令
            let javascriptCommand = "WebViewJavascriptBridge._handleMessageFromObjC('\(jsonString.replacingOccurrences(of: "'", with: "\\'"))');"
            // 在主线程上执行JavaScript
            DispatchQueue.main.async {
                self.webView.evaluateJavaScript(javascriptCommand, completionHandler: { (result, error) in
                    if let error = error {
                        print("发送标准响应到JS失败: \(error)")
                    }
                })
            }
        }
    }
    
    // 向JS发送成功响应（标准格式）
    private func sendSuccessToJS(callbackId: String, data: Any? = nil, msg: String = "操作成功") {
        sendStandardResponseToJS(callbackId: callbackId, data: data, status: 1, msg: msg)
    }
    
    // 向JS发送失败响应（标准格式）
    private func sendFailureToJS(callbackId: String, msg: String = "操作失败", data: Any? = nil) {
        sendStandardResponseToJS(callbackId: callbackId, data: data, status: 0, msg: msg)
    }

    // 直接发送响应数据到JS，不使用外层包装
    private func sendDirectResponseToJS(callbackId: String, responseData: [String: Any]) {
        // 使用WebViewJavascriptBridge的原始方法直接发送响应数据
        let javascriptCommand = """
        WebViewJavascriptBridge._handleMessageFromObjC('{"responseId":"\(callbackId)","responseData":\(self.jsonStringFromDictionary(responseData))}');
        """
        
        // 在主线程上执行JavaScript
        DispatchQueue.main.async {
            self.webView.evaluateJavaScript(javascriptCommand, completionHandler: { (result, error) in
                if let error = error {
                    print("直接发送响应到JS失败: \(error)")
                }
            })
        }
    }
    
    // 将字典转换为JSON字符串
    private func jsonStringFromDictionary(_ dict: [String: Any]) -> String {
        if let jsonData = try? JSONSerialization.data(withJSONObject: dict, options: []),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            return jsonString
        }
        return "{}" // 失败时返回空对象
    }

    // MARK: - URL 变化监控
    
    // 注册 JS 回调来监听 URL 变化
    private func setupURLMonitoring() {
        // 注入 JavaScript 监听 hashchange 和 popstate 事件
        let script = """
        function notifyURLChange() {
            window.webkit.messageHandlers.urlChangeObserver.postMessage({
                url: window.location.href,
                path: window.location.pathname + window.location.hash
            });
        }
        
        // 监听 hashchange 事件（用于 hash 路由变化，如 /#/page1 -> /#/page2）
        window.addEventListener('hashchange', notifyURLChange);
        
        // 监听 popstate 事件（用于 history API 路由变化）
        window.addEventListener('popstate', notifyURLChange);
        
        // 对 history.pushState 和 replaceState 进行重写，以便捕获 SPA 路由变化
        (function() {
            const originalPushState = history.pushState;
            const originalReplaceState = history.replaceState;
            
            history.pushState = function() {
                originalPushState.apply(this, arguments);
                notifyURLChange();
            };
            
            history.replaceState = function() {
                originalReplaceState.apply(this, arguments);
                notifyURLChange();
            };
        })();
        
        // 初始通知
        notifyURLChange();
        """
        
        // 创建用户脚本
        let userScript = WKUserScript(source: script, injectionTime: .atDocumentEnd, forMainFrameOnly: true)
        webView.configuration.userContentController.addUserScript(userScript)
        
        // 添加消息处理程序
        webView.configuration.userContentController.add(self, name: "urlChangeObserver")
    }
    
    // KVO 观察 webView.url 变化
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if keyPath == #keyPath(WKWebView.url) {
            if let url = webView.url {
                print("WebView URL 变化 (KVO): \(url.absoluteString)")
                checkAndUpdateNavBarForSpecialPaths(urlString: url.absoluteString)
            }
            // 已处理，无需调用 super，避免未处理异常
            return
        }
        super.observeValue(forKeyPath: keyPath, of: object, change: change, context: context)
    }
    
    // 检查是否为需要导航栏的特殊路径
    private func checkAndUpdateNavBarForSpecialPaths(urlString: String) {
        // 解析 URL 和路径（包括 hash 部分）
        guard let url = URL(string: urlString) else {
            print("无法解析URL: \(urlString)")
            return
        }
        
        // 如果 URL 域名与 baseH5Domain 不同，则始终显示原生导航栏
        if let baseURL = URL(string: WebViewController.baseH5Domain), url.host != baseURL.host {
            if shouldHideNavigationBar {
                shouldHideNavigationBar = false
                updateNavigationBarVisibility()
            }
            return
        }

        // 如果外部已强制设置导航栏可见性，则不再自动调整
        if forceShowNavBar {
            if shouldHideNavigationBar {
                shouldHideNavigationBar = false
                updateNavigationBarVisibility()
            }
            return
        }
        
        // 首页判断 - 首页需要显示导航栏
        let isHomePage = isHomeURL(url)
        if isHomePage {
            print("检测到首页URL，显示导航栏")
            // 设置首页标题
            if navTitle != "首页" {
                navTitle = "首页"
            }
            if shouldHideNavigationBar {
                shouldHideNavigationBar = false
                updateNavigationBarVisibility()
            }
            return
        }
        
        // 对于 SPA，检查 hash 部分 (#/xxx)
        var pathToCheck = url.path
        if let fragment = url.fragment, !fragment.isEmpty {
            // 处理 fragment 中可能包含的查询参数
            let fragmentComponents = fragment.components(separatedBy: "?")
            var cleanFragment = fragmentComponents[0]
            
            // 移除开头的 #/
            if cleanFragment.hasPrefix("#") {
                cleanFragment = String(cleanFragment.dropFirst())
            }
            if cleanFragment.hasPrefix("/") {
                cleanFragment = String(cleanFragment.dropFirst())
            }
            
            pathToCheck = "/" + cleanFragment
        }
        
        print("解析后的路径: \(pathToCheck)")
        
        // 检查是否在特殊路径列表中
        let needsNavBar = WebViewController.specialPathsNeedingNavBar.contains { specialPath in
            let comparePath = pathToCheck.trimmingCharacters(in: CharacterSet(charactersIn: "/"))
            let compareSpecial = specialPath.trimmingCharacters(in: CharacterSet(charactersIn: "/"))
            return comparePath == compareSpecial || comparePath.hasPrefix(compareSpecial + "/")
        }

        print("路径[\(pathToCheck)]是否需要导航栏: \(needsNavBar)")
        
        // 如果当前路径状态与导航栏状态不匹配，则更新导航栏
        if needsNavBar == shouldHideNavigationBar {
            shouldHideNavigationBar = !needsNavBar
            print("更新导航栏可见性: \(needsNavBar ? "显示" : "隐藏")")
            updateNavigationBarVisibility()
        }

        // 动态更新标题（URL 变化时）
        if let dynamicTitle = extractTitleFromURL(url) {
            if dynamicTitle != navTitle {
                DispatchQueue.main.async { [weak self] in
                    self?.navTitle = dynamicTitle
                }
            }
        }
    }
    
    private func updateNavigationBarVisibility() {
        // 直接使用 BaseViewController 的导航栏控制
        showNavBar = !shouldHideNavigationBar
    }
    
    // MARK: - Notification Handling
    @objc private func handleTokenSaved(_ notification: Notification) {
        guard let token = notification.userInfo?["token"] as? String, !token.isEmpty else { return }
        print("[WebViewController] Received tokenSaved notification, sending token to JS via onSaveToken")

        // 构建token数据
        let tokenData: [String: Any] = ["token": token]

        // 使用统一的JSON发送方法
        sendJSONToBridge(handlerName: "onSaveToken", data: tokenData)
    }

    // 清理工作
    deinit {
        NotificationCenter.default.removeObserver(self, name: .tokenSaved, object: nil)
        NotificationCenter.default.removeObserver(self, name: .wxMiniProgramPaymentResult, object: nil)
        // 移除 URL 观察者
        webView.removeObserver(self, forKeyPath: #keyPath(WKWebView.url))
        // 移除消息处理程序
        webView.configuration.userContentController.removeScriptMessageHandler(forName: "urlChangeObserver")
    }
    
    /* --------------- 以下调试方法已弃用 ---------------
    private func debugBridgeState() {
        // 调试代码已移除
    }
    -------------------------------------------------- */
    
    // 判断URL是否为首页
    private func isHomeURL(_ url: URL) -> Bool {
        // 检查是否是基础域名（没有路径或只有/?或/?#或/?#/）
        // 从 baseH5Domain 中提取出主机部分
        guard let baseURL = URL(string: WebViewController.baseH5Domain) else { return false }
        
        // 检查主机部分是否匹配
        guard url.host == baseURL.host else { return false }
        
        // 检查路径部分
        let path = url.path.trimmingCharacters(in: .whitespacesAndNewlines)
        if path != "" && path != "/" {
            return false
        }
        
        // 检查片段部分
        if let fragment = url.fragment {
            // 如果片段非空，检查是否为空路径
            var cleanFragment = fragment.trimmingCharacters(in: CharacterSet(charactersIn: "#/"))
            
            // 检查是否有查询参数
            if cleanFragment.contains("?") {
                cleanFragment = cleanFragment.components(separatedBy: "?")[0]
            }
            
            // 空路径或仅包含"?"表示是首页
            return cleanFragment.isEmpty || cleanFragment == "?"
        }
        
        // 没有片段，没有特殊路径 - 认为是首页
        return true
    }

    // MARK: - 标题处理
    
    /// 从 URL 路径中提取标题
    private func extractTitleFromURL(_ url: URL) -> String? {
        // 获取 URL 的路径部分或片段（针对 SPA）
        var pathToCheck: String
        
        if let fragment = url.fragment, !fragment.isEmpty {
            // 处理片段中可能包含的查询参数
            let fragmentComponents = fragment.components(separatedBy: "?")
            pathToCheck = fragmentComponents[0]
            
            // 移除开头的 #/
            if pathToCheck.hasPrefix("#") {
                pathToCheck = String(pathToCheck.dropFirst())
            }
            if pathToCheck.hasPrefix("/") {
                pathToCheck = String(pathToCheck.dropFirst())
            }
        } else {
            pathToCheck = url.path.trimmingCharacters(in: .whitespacesAndNewlines)
            if pathToCheck.hasPrefix("/") {
                pathToCheck = String(pathToCheck.dropFirst())
            }
        }
        
        // 检查是否是首页
        if pathToCheck.isEmpty || pathToCheck == "/" {
            return "首页"
        }
        
        // 页面路径与标题映射
        let pathTitleMap: [String: String] = [
            "pages/news/news": "新闻资讯",
            "pages/live/live": "直播",
            "pages/goodCart/goodCart": "购物车",
            "pages/user/user": "个人中心",
            "goodPage/goodDetail": "商品详情",
            "pages/goodDetail": "商品详情"
        ]
        
        // 尝试精确匹配
        if let title = pathTitleMap[pathToCheck] {
            return title
        }
        
        // 尝试前缀匹配
        for (path, title) in pathTitleMap {
            if pathToCheck.hasPrefix(path) {
                return title
            }
        }
        
        // 尝试基于目录结构分析
        let components = pathToCheck.components(separatedBy: "/")
        if components.count >= 2 {
            // 检查最后一个组件是否为页面类型标识
            let lastComponent = components.last ?? ""
            
            // 常见页面类型映射
            let pageTypeMap: [String: String] = [
                "list": "列表",
                "detail": "详情",
                "edit": "编辑",
                "profile": "个人信息",
                "settings": "设置",
                "search": "搜索",
                "cart": "购物车",
                "order": "订单",
                "live": "直播",
                "news": "新闻",
                "user": "用户中心"
            ]
            
            // 匹配常见页面类型
            for (type, title) in pageTypeMap {
                if lastComponent.contains(type) {
                    // 如果有上级目录，尝试加上上级目录名称
                    if components.count > 2 {
                        let parentDir = components[components.count - 2]
                        return "\(parentDir)\(title)"
                    }
                    return title
                }
            }
            
            // 如果没有特定匹配，返回最后一个路径组件作为标题
            return lastComponent.capitalized
        }
        
        // 无法识别的路径，返回 nil 以使用默认值
        return nil
    }

    // 创建一个特定用于 token 响应的方法，尝试不同的格式来匹配 H5 端的期望
    private func sendTokenResponse(callbackId: String? = nil, responseCallback: ((Any?) -> Void)? = nil) {
        // 从本地（UserDefaults）获取 token
        let token = UserDefaults.standard.string(forKey: "userToken") ?? ""

        // 如果未获取到 token，则返回失败状态 0
        let isTokenAvailable = !token.isEmpty
        let response: [String: Any] = [
            "data": isTokenAvailable ? ["token": token] : NSNull(),
            "errMsg": isTokenAvailable ? NSNull() : "未找到本地 token",
            "msg": isTokenAvailable ? "成功" : "失败",
            "status": isTokenAvailable ? 1 : 0
        ]

        // DEBUG: 打印即将发送给 H5 的 token 响应内容
        print("[WebViewController] sendTokenResponse -> outgoing response: \(response)")
        
        if let callback = responseCallback {
            // 使用JSON字符串回调方式
            sendJSONResponse(responseCallback: callback, data: response, handlerName: "goGetToken")
        } else if let cbId = callbackId {
            // 使用扁平结构的 JSON（去掉 responseData 包裹，直接平级返回）
            let flatResponse: [String: Any] = [
                "responseId": cbId,
                "data": isTokenAvailable ? ["token": token] : NSNull(),
                "errMsg": isTokenAvailable ? NSNull() : "未找到本地 token",
                "msg": isTokenAvailable ? "成功" : "失败",
                "status": isTokenAvailable ? 1 : 0
            ]

            // DEBUG: 打印扁平结构的响应，方便排查 JS 端回调内容
            print("[WebViewController] sendTokenResponse -> flatResponse: \(flatResponse)")
            
            let jsonString = jsonStringFromDictionary(flatResponse)
            let jsCommand = "WebViewJavascriptBridge._handleMessageFromObjC('\(jsonString)');"
            
            DispatchQueue.main.async { [weak self] in
                self?.webView.evaluateJavaScript(jsCommand, completionHandler: { (_, error) in
                    if let error = error {
                        print("Token响应发送失败: \(error)")
                    } else {
                        print("Token响应已发送 (扁平结构)")
                    }
                })
            }
        }
    }

    /*
     MARK: - 示例方法：responseData 中返回 JSON 字符串
     说明：当 H5 端只能解析 responseData 为 JSON 字符串时，可参考此实现。
    */
    /*
    private func sendTokenResponseAsJSONString(callbackId: String) {
        let tokenPayload: [String: Any] = [
            "data": ["token": "<Your_Token_Here>"],
            "errMsg": NSNull(),
            "msg": "成功",
            "status": 1
        ]
        guard let payloadJSON = jsonStringFromDictionary(tokenPayload) else { return }
        let responseMessage: [String: Any] = [
            "responseId": callbackId,
            // 注意：这里的 responseData 传入 JSON 字符串
            "responseData": payloadJSON
        ]
        let messageJSON = jsonStringFromDictionary(responseMessage)
        let jsCommand = "WebViewJavascriptBridge._handleMessageFromObjC('\(messageJSON)');"
        DispatchQueue.main.async { [weak self] in
            self?.webView.evaluateJavaScript(jsCommand, completionHandler: nil)
        }
    }
    */
    
    /* --------------- 以下测试按钮注入方法已弃用 ---------------
    private func injectTestButton() {
        // 测试代码已移除
    }
    -------------------------------------------------------- */
}

// MARK: - WebView 缓存清理
extension WebViewController {
    /// 退出登录时调用，清理 WKWebView 的 Cookies 与网站数据，避免残留登录状态。
    /// - Parameter completion: 清理完成后的回调，可选。
    static func clearLoginCache(completion: (() -> Void)? = nil) {
        // 1. 清理共享 HTTPCookieStorage 中的 Cookie
        if let cookies = HTTPCookieStorage.shared.cookies {
            for cookie in cookies {
                HTTPCookieStorage.shared.deleteCookie(cookie)
            }
        }

        // 2. 清理 WKWebsiteDataStore 中的数据（包括 LocalStorage / IndexedDB 等）
        let dataStore = WKWebsiteDataStore.default()
        let dataTypes = WKWebsiteDataStore.allWebsiteDataTypes()
        // 选择足够早的时间以删除所有记录
        let sinceDate = Date(timeIntervalSince1970: 0)
        dataStore.removeData(ofTypes: dataTypes, modifiedSince: sinceDate) {
            print("[WebViewController] WebView 登录缓存已清理")
            completion?()
        }
    }
}

// MARK: - WKNavigationDelegate
extension WebViewController: WKNavigationDelegate {
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        loadingIndicator.stopAnimating()
        
        // 打印当前页面URL，方便确认特殊路径
        if let currentURL = webView.url {
            print("WebView当前URL: \(currentURL.absoluteString)")
            print("WebView当前路径: \(currentURL.path)")
            
            // 如果 titleText 为空，尝试从路径提取标题
            if shouldUseWebPageTitle && titleText == nil {
                if let extractedTitle = extractTitleFromURL(currentURL) {
                    self.navTitle = extractedTitle
                }
            }
            
            // (测试代码已移除)
        }

        // 如果需要在加载完成后隐藏导航栏（仅路径初始化且非特殊路径）
        if hideNavBarAfterLoad && !shouldHideNavigationBar {
            shouldHideNavigationBar = true
            updateNavigationBarVisibility()
        }

        // 如果没有传入标题，则使用网页的标题
        if shouldUseWebPageTitle {
            webView.evaluateJavaScript("document.title") { [weak self] (result, error) in
                if let title = result as? String, !title.isEmpty {
                    DispatchQueue.main.async {
                        self?.navTitle = title
                    }
                }
            }
        }

        // 测试bridge连通性（延迟执行确保H5完全加载）
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) { [weak self] in
            self?.testBridgeConnectivity()
        }
    }
    
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        loadingIndicator.stopAnimating()
        // 处理加载失败
        handleLoadError(error)
    }

    // 新增：首屏加载失败（包括网络不可达等）
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        loadingIndicator.stopAnimating()
        handleLoadError(error)
    }
    
    // 如果WebViewJavascriptBridge需要处理decidePolicyForNavigationAction，
    // 它通常会通过setWebViewDelegate来处理，或者需要您手动调用。
    // 查阅您使用的WebViewJavascriptBridge版本的文档以确认。
    // func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, decisionHandler: @escaping (WKNavigationActionPolicy) -> Void) {
    //     if bridge.isWebViewJavascriptBridgeURL(navigationAction.request.url) {
    //         bridge.decidePolicy(for: navigationAction)
    //         decisionHandler(.cancel)
    //         return
    //     }
    //     // Your other decision logic
    //     decisionHandler(.allow)
    // }
}

// MARK: - CLLocationManagerDelegate
extension WebViewController {
    // 位置更新回调
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        
        // 停止定位，节省电量
        locationManager?.stopUpdatingLocation()
        print("【定位回调】已停止定位更新")
        
        let latitude = location.coordinate.latitude
        let longitude = location.coordinate.longitude
        
        print("【定位回调】获取到位置: 纬度 \(latitude), 经度 \(longitude)")
        
        // 获取详细地址
        print("【定位回调】开始地理编码获取详细地址")
        let geocoder = CLGeocoder()
        geocoder.reverseGeocodeLocation(location) { [weak self] (placemarks, error) in
            guard let self = self else { return }
            
            if let error = error {
                print("【定位回调】地理编码失败: \(error)")
                // 使用回调返回错误
                self.locationResponseCallback?(["data": NSNull(), "status": 0, "msg": "获取地址失败"])
                return
            }
            
            guard let placemark = placemarks?.first else {
                print("【定位回调】未找到地标信息")
                // 虽然没有详细地址，但有经纬度，仍然返回成功
                let locationData: [String: String] = [
                    "latitude": "\(latitude)",
                    "longitude": "\(longitude)",
                    "address": "",
                "province": "",
                "city": "",
                "district": "",
                "name": ""
                ]
                print("【定位回调】返回位置数据(无详细地址): \(locationData)")
                self.locationResponseCallback?(["data": locationData, "status": 1, "msg": "获取位置成功(无详细地址)"])
                return
            }
            
            // 提取各个地址组件
            let country = placemark.country ?? ""
            let province = placemark.administrativeArea ?? ""  // 省/直辖市
            let city = placemark.locality ?? ""               // 市
            let district = placemark.subLocality ?? ""         // 区/县
            let street = placemark.thoroughfare ?? ""          // 街道
            let streetNumber = placemark.subThoroughfare ?? "" // 门牌号

            // 构建完整地址
            var addressComponents: [String] = []
            if !country.isEmpty { addressComponents.append(country) }
            if !province.isEmpty { addressComponents.append(province) }
            if !city.isEmpty { addressComponents.append(city) }
            if !district.isEmpty { addressComponents.append(district) }
            if !street.isEmpty { addressComponents.append(street) }
            if !streetNumber.isEmpty { addressComponents.append(streetNumber) }

            let fullAddress = addressComponents.joined(separator: "")

            // 构建街道地址（街道+门牌号）
            var streetAddress = ""
            if !street.isEmpty || !streetNumber.isEmpty {
                streetAddress = [street, streetNumber].filter { !$0.isEmpty }.joined(separator: "")
            }

            print("【定位回调】详细地址解析:")
            print("  完整地址: \(fullAddress)")
            print("  省份: \(province)")
            print("  城市: \(city)")
            print("  区县: \(district)")
            print("  街道地址: \(streetAddress)")

            // 返回数据给JS（按照新的格式要求）
            let locationData: [String: String] = [
                "latitude": "\(latitude)",      // 纬度全称
                "longitude": "\(longitude)",    // 经度全称
                "address": fullAddress,         // 长地址
                "province": province,           // 省
                "city": city,                   // 市
                "district": district,           // 区
                "name": streetAddress           // 街道地址
            ]
            print("【定位回调】返回完整位置数据: \(locationData)")
            self.locationResponseCallback?(["data": locationData, "status": 1, "msg": "获取位置成功"])
        }
    }
    
    // 定位失败回调
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("【定位回调】定位失败: \(error)")
        locationManager?.stopUpdatingLocation()
        
        // 使用回调返回错误
        locationResponseCallback?(["data": NSNull(), "status": 0, "msg": "定位失败: \(error.localizedDescription)"])
    }
    
    // 授权状态变化回调
    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        print("【定位回调】授权状态变化: \(status.rawValue)")
        
        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            // 用户授权了定位权限，开始定位
            print("【定位回调】用户授权了定位权限，开始定位")
            locationManager?.startUpdatingLocation()
        case .denied, .restricted:
            // 用户拒绝了定位权限
            print("【定位回调】用户拒绝了定位权限")
            // 使用回调返回错误
            locationResponseCallback?(["data": NSNull(), "status": 0, "msg": "定位权限被拒绝"])
        default:
            print("【定位回调】等待用户授权决定")
            break
        }
    }
}

// 辅助方法 (可选, 如果需要弹窗提示)
extension WebViewController {
    private func showAlert(message: String) {
        let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default, handler: nil))
        present(alert, animated: true, completion: nil)
    }
}

// MARK: - WKScriptMessageHandler
extension WebViewController: WKScriptMessageHandler {
    func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
        if message.name == "urlChangeObserver" {
            // 解析消息内容
            guard let body = message.body as? [String: Any],
                  let url = body["url"] as? String else {
                return
            }
            
            print("WebView URL 变化 (JS): \(url)")
            if let path = body["path"] as? String {
                print("WebView Path 变化 (JS): \(path)")
            }
            
            // 检查是否是特殊路径，需要显示导航栏
            checkAndUpdateNavBarForSpecialPaths(urlString: url)
        }
    }
}

// MARK: - Load Error Handling
extension WebViewController {
    private func handleLoadError(_ error: Error) {
        print("WebView load error: \(error.localizedDescription)")
        // 弹出错误提示
        showAlert(message: "页面加载失败，请检查网络后重试")

        // 若最初隐藏了导航栏，为避免无法返回，则强制显示
        if shouldHideNavigationBar {
            shouldHideNavigationBar = false
            updateNavigationBarVisibility()
        }

        // 更新标题
        navTitle = "加载失败"
    }
}

// MARK: - Payment Handling
extension WebViewController {

    /// 处理微信支付（通过微信小程序）
    private func handleWechatPay(payData: [String: Any]) {
        print("【微信支付】开始处理微信支付（小程序方式）")

        // 检查微信是否安装
        guard WeChatMiniProgramManager.shared.canLaunchMiniProgram() else {
            let status = WeChatMiniProgramManager.shared.getMiniProgramStatusDescription()
            print("【微信支付】微信小程序不可用: \(status)")
            showAlert(message: status)
            notifyPaymentResult(status: 0)
            return
        }

        // 设置支付状态
        isPaying = true
        payType = wxPayType

        // 构建小程序路径参数（与Android PayBean.toString()格式一致）
        let payDataString = convertPayDataToAndroidFormat(payData)
        let miniProgramPath = "\(cashierPagePath)?\(payDataString)"

        print("【微信支付】小程序路径: \(miniProgramPath)")

        // 跳转到微信小程序
        WeChatMiniProgramManager.shared.launchMiniProgram(
            userName: wxMiniProgramId,
            path: miniProgramPath,
            miniProgramType: .release
        ) { [weak self] success, errorMessage in
            DispatchQueue.main.async {
                if success {
                    print("【微信支付】成功跳转到微信小程序")
                    // 跳转成功，等待用户返回时处理支付结果
                } else {
                    print("【微信支付】跳转微信小程序失败: \(errorMessage ?? "未知错误")")
                    self?.showAlert(message: errorMessage ?? "跳转微信小程序失败")
                    self?.notifyPaymentResult(status: 0)
                }
            }
        }
    }

    /// 将支付数据转换为Android PayBean.toString()格式
    private func convertPayDataToAndroidFormat(_ payData: [String: Any]) -> String {
        // 提取payInfo
        let payInfo: String
        if let payInfoDict = payData["payInfo"] as? [String: Any] {
            // 如果payInfo是字典，转换为JSON字符串
            do {
                let jsonData = try JSONSerialization.data(withJSONObject: payInfoDict, options: [])
                payInfo = String(data: jsonData, encoding: .utf8) ?? ""
            } catch {
                print("【微信支付】payInfo JSON转换失败: \(error)")
                payInfo = ""
            }
        } else if let payInfoString = payData["payInfo"] as? String {
            // 如果payInfo已经是字符串，直接使用
            payInfo = payInfoString
        } else {
            payInfo = ""
        }

        // 提取orderDetail并转换为JSON字符串
        let orderDetailString: String
        if let orderDetail = payData["orderDetail"] as? [String: Any] {
            do {
                let jsonData = try JSONSerialization.data(withJSONObject: orderDetail, options: [])
                orderDetailString = String(data: jsonData, encoding: .utf8) ?? ""
            } catch {
                print("【微信支付】orderDetail JSON转换失败: \(error)")
                orderDetailString = ""
            }
        } else {
            orderDetailString = ""
        }

        // 构建与Android PayBean.toString()相同的格式：payInfo=xxx&orderDetail=xxx
        let encodedPayInfo = payInfo.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let encodedOrderDetail = orderDetailString.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""

        let result = "payInfo=\(encodedPayInfo)&orderDetail=\(encodedOrderDetail)"
        print("【微信支付】构建的参数字符串: \(result)")

        return result
    }

    /// 处理支付宝支付
    private func handleAlipay(payData: [String: Any]) {
        print("【支付宝支付】开始处理支付宝支付")

        // 解析支付信息
        guard let payInfo = payData["payInfo"] as? String else {
            print("【支付宝支付】缺少支付信息")
            showAlert(message: "支付信息错误")
            notifyPaymentResult(status: 0) // 0: 支付失败
            return
        }

        print("【支付宝支付】支付信息: \(payInfo)")

        // 直接跳转到Safari进行支付
        openAlipayInSafari(url: payInfo)
    }







    /// 在Safari中打开支付宝支付
    private func openAlipayInSafari(url: String) {
        guard let paymentURL = URL(string: url) else {
            print("【支付宝支付】无效的支付URL")
            showAlert(message: "支付链接无效")
            notifyPaymentResult(status: 0) // 0: 支付失败
            return
        }

        DispatchQueue.main.async {
            if UIApplication.shared.canOpenURL(paymentURL) {
                print("【支付宝支付】正在打开Safari进行支付")
                UIApplication.shared.open(paymentURL, options: [:]) { [weak self] success in
                    if success {
                        print("【支付宝支付】成功打开Safari，返回成功状态")
                        // 跳转成功，立即返回成功状态，H5页面会处理后续支付结果查询
                        self?.notifyPaymentResult(status: 3) // 1: 支付成功（跳转成功）
                    } else {
                        print("【支付宝支付】无法打开Safari")
                        self?.showAlert(message: "无法打开支付页面")
                        self?.notifyPaymentResult(status: 0) // 0: 支付失败
                    }
                }
            } else {
                print("【支付宝支付】无法打开支付URL")
                self.showAlert(message: "无法打开支付页面")
                self.notifyPaymentResult(status: 0) // 0: 支付失败
            }
        }
    }

    /// 通知H5支付结果
    private func notifyPaymentResult(status: Int) {
        print("【支付结果】通知H5支付结果: \(status)")

        // 验证状态码有效性（参考Android代码）
        let validStates = [0, 1, 2, 3] // 0：支付失败，1：支付成功，2：取消支付，3：未知状态
        guard validStates.contains(status) else {
            print("【支付结果】无效的支付状态码: \(status)")
            return
        }

        // 重置支付状态
        isPaying = false
        payType = ""

        // 构建支付结果数据
        let paymentData: [String: Any] = [
            "paymentStatus": status
        ]

        print("【支付结果】准备发送支付结果JSON到H5: \(paymentData)")

        // 延迟一下确保H5页面完全准备好
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            guard let self = self else {
                print("【支付结果】延迟发送时self已释放")
                return
            }

            // 使用统一的JSON发送方法
            self.sendJSONToBridge(handlerName: "onPayment", data: paymentData)
        }
    }

    /// 统一的JSON桥接发送方法
    private func sendJSONToBridge(handlerName: String, data: [String: Any]) {
        guard let bridge = bridge else {
            print("【JSON桥接】Bridge未初始化，无法发送数据到: \(handlerName)")
            return
        }

        do {
            // 将数据转换为JSON字符串
            let jsonData = try JSONSerialization.data(withJSONObject: data, options: [])
            guard let jsonString = String(data: jsonData, encoding: .utf8) else {
                print("【JSON桥接】JSON字符串转换失败: \(handlerName)")
                return
            }

            print("【JSON桥接】发送JSON到H5 [\(handlerName)]: \(jsonString)")

            // 发送JSON字符串到H5
            bridge.callHandler(handlerName, data: jsonString)

            print("【JSON桥接】已发送JSON到H5: \(handlerName)")
        } catch {
            print("【JSON桥接】JSON序列化失败 [\(handlerName)]: \(error)")
        }
    }

    /// 统一的JSON响应回调方法
    private func sendJSONResponse(responseCallback: WVJBResponseCallback?, data: [String: Any], handlerName: String = "unknown") {
        guard let responseCallback = responseCallback else {
            print("【JSON响应】responseCallback为nil，无法发送响应: \(handlerName)")
            return
        }

        do {
            // 将数据转换为JSON字符串
            let jsonData = try JSONSerialization.data(withJSONObject: data, options: [])
            guard let jsonString = String(data: jsonData, encoding: .utf8) else {
                print("【JSON响应】JSON字符串转换失败: \(handlerName)")
                responseCallback(["error": "JSON转换失败"])
                return
            }

            print("【JSON响应】发送JSON响应 [\(handlerName)]: \(jsonString)")

            // 发送JSON字符串作为响应
            responseCallback(jsonString)

            print("【JSON响应】已发送JSON响应: \(handlerName)")
        } catch {
            print("【JSON响应】JSON序列化失败 [\(handlerName)]: \(error)")
            responseCallback(["error": "JSON序列化失败: \(error.localizedDescription)"])
        }
    }

    /// 测试bridge连通性
    private func testBridgeConnectivity() {
        guard bridge != nil else {
            print("【Bridge测试】Bridge未初始化")
            return
        }

        print("【Bridge测试】开始测试bridge连通性")

        // 测试发送一个简单的JSON消息到H5
        let testData = ["test": "bridge_connectivity", "timestamp": Date().timeIntervalSince1970] as [String : Any]

        // 使用统一的JSON发送方法
        sendJSONToBridge(handlerName: "onTest", data: testData)

        print("【Bridge测试】已发送JSON测试消息到H5")
    }

    /// 导航到首页
    private func navigateToHome() {
        print("【导航】开始导航到首页")

        DispatchQueue.main.async {
            // 获取当前的窗口和根视图控制器
            guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                  let window = windowScene.windows.first,
                  let tabBarController = window.rootViewController as? CustomTabBarController else {
                print("【导航】无法获取TabBarController")
                return
            }

            // 切换到首页Tab（索引0）
            tabBarController.selectTab(at: 0)
            print("【导航】已切换到首页Tab")

            // 如果当前WebViewController在导航栈中，则返回到根视图控制器
            if let navigationController = self.navigationController {
                navigationController.popToRootViewController(animated: true)
                print("【导航】已返回到根视图控制器")
            } else if self.presentingViewController != nil {
                // 如果是模态展示的，则dismiss
                self.dismiss(animated: true) {
                    print("【导航】已关闭模态页面")
                }
            }
        }
    }

    /// 打开地图导航
    private func openMapNavigation(latitude: String, longitude: String, name: String) {
        print("【地图导航】开始打开地图导航")

        // 验证坐标
        guard let lat = Double(latitude), let lng = Double(longitude) else {
            print("【地图导航】坐标格式错误")
            showAlert(message: "坐标格式错误")
            return
        }

        DispatchQueue.main.async {
            // 构建地图URL
            let destinationName = name.isEmpty ? "目的地" : name

            // 优先尝试打开高德地图
            let amapURLString = "iosamap://navi?sourceApplication=树小柒&poiname=\(destinationName)&lat=\(lat)&lon=\(lng)&dev=0"
            if let amapURL = URL(string: amapURLString.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""),
               UIApplication.shared.canOpenURL(amapURL) {
                print("【地图导航】使用高德地图导航")
                UIApplication.shared.open(amapURL, options: [:]) { success in
                    print("【地图导航】高德地图打开结果: \(success)")
                }
                return
            }

            // 尝试打开百度地图
            let baiduURLString = "baidumap://map/direction?destination=latlng:\(lat),\(lng)|name:\(destinationName)&mode=driving"
            if let baiduURL = URL(string: baiduURLString.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""),
               UIApplication.shared.canOpenURL(baiduURL) {
                print("【地图导航】使用百度地图导航")
                UIApplication.shared.open(baiduURL, options: [:]) { success in
                    print("【地图导航】百度地图打开结果: \(success)")
                }
                return
            }

            // 最后使用系统地图
            let appleMapURLString = "http://maps.apple.com/?daddr=\(lat),\(lng)&dirflg=d"
            if let appleMapURL = URL(string: appleMapURLString) {
                print("【地图导航】使用苹果地图导航")
                UIApplication.shared.open(appleMapURL, options: [:]) { success in
                    print("【地图导航】苹果地图打开结果: \(success)")
                }
            } else {
                print("【地图导航】无法打开任何地图应用")
                self.showAlert(message: "无法打开地图应用")
            }
        }
    }

    /// 处理分享
    private func handleShare(shareData: [String: Any]) {
        print("【分享功能】开始处理分享")

        // 解析分享数据
        let path = shareData["path"] as? String ?? ""
        let title = shareData["title"] as? String ?? ""
        let imageUrl = shareData["imageUrl"] as? String ?? ""

        print("【分享功能】分享路径: \(path)")
        print("【分享功能】分享标题: \(title)")
        print("【分享功能】分享图片: \(imageUrl)")

        // 验证必要的分享数据
        guard !path.isEmpty else {
            print("【分享功能】缺少分享路径")
            showAlert(message: "分享内容错误")
            return
        }

        DispatchQueue.main.async {
            // 构建分享内容
            let sharePayload = SharePayload(
                title: title.isEmpty ? "树小柒分享" : title,
                description: "来自树小柒的精彩内容",
                link: path,
                thumbnail: imageUrl.isEmpty ? nil : imageUrl,
                image: nil,
                type: .text,
                extras: [
                    "path": path,
                    "title": title,
                    "imageUrl": imageUrl
                ]
            )

            // 发送分享通知
            print("【分享功能】发送分享通知")
            NotificationCenter.default.post(name: .shareRequested, object: sharePayload)
        }
    }

    /// 返回上一页
    private func goBackPrevPage() {
        print("【返回上一页】开始执行返回操作")

        DispatchQueue.main.async {
            // 检查是否有导航控制器且可以返回
            if let navigationController = self.navigationController,
               navigationController.viewControllers.count > 1 {
                print("【返回上一页】通过导航控制器返回")
                navigationController.popViewController(animated: true)
            } else if self.presentingViewController != nil {
                // 如果是模态展示的，则dismiss
                print("【返回上一页】关闭模态页面")
                self.dismiss(animated: true)
            } else {
                // 如果无法返回，尝试关闭当前页面
                print("【返回上一页】无法确定返回方式，尝试关闭当前页面")
                if let tabBarController = self.tabBarController {
                    // 如果在TabBar中，切换到首页
                    if let customTabBar = tabBarController as? CustomTabBarController {
                        customTabBar.selectTab(at: 0)
                    } else {
                        tabBarController.selectedIndex = 0
                    }
                } else {
                    // 最后的选择：尝试dismiss
                    self.dismiss(animated: true)
                }
            }
        }
    }
}

