//
//  GoldCoinSystemTaskCenterViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/8/12.
//

import UIKit
import SnapKit

// MARK: - 金币系统任务中心控制器
class GoldCoinSystemTaskCenterViewController: BaseViewController {

    // MARK: - 数据模型
    private var currentGoldCoins: Int = 8888
    private let defaultRewardCoins: Int = 888
    private var dailyEarnableCoins: Int = 2000
    private var exchangeRate: String = "10000金币=1元现金"

    // 签到数据
    private var checkInDays: [CheckInDay] = []
    private var consecutiveCheckInDays: Int = 2 // 当前连续签到天数（0表示今天还没签到）
    // 今日是否已点击过签到按钮（由外部传入，后续由服务端控制）
    private var hasClickedCheckInButtonToday: Bool = false

    // 观看视频进度
    private var videoWatchProgress: Float = 0.3 // 30%
    private var videoWatchedSeconds: Int = 180 // 已观看3分钟
    private var videoTotalSeconds: Int = 600 // 总共10分钟

    // MARK: - UI 组件

    // 滚动视图
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .white
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()

    // 滚动内容容器
    private lazy var scrollContentView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    // 头部背景容器 - #F7F7F7色，20圆角，168高度
    private lazy var headerBackgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F7F7F7")
        view.layer.cornerRadius = 20
        return view
    }()

    // 当前金币背景图 - 橙色背景
    private lazy var goldCoinBackgroundView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "gold_coin_task_center_header_bg") // 预命名的橙色背景图
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 16
        imageView.isUserInteractionEnabled = true
        return imageView
    }()

    // 当前金币标题
    private lazy var currentGoldTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "当前金币"
        label.textColor = .white
        label.font = .systemFont(ofSize: 14, weight: .medium)
        return label
    }()

    // 金币数量
    private lazy var goldCoinAmountLabel: UILabel = {
        let label = UILabel()
        label.text = "\(currentGoldCoins)"
        label.textColor = .white
        label.font = .systemFont(ofSize: 24, weight: .bold)
        return label
    }()

    // 提现按钮
    private lazy var withdrawButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("提现", for: .normal)
        button.setTitleColor(.appThemeOrange, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.backgroundColor = .white
        button.layer.cornerRadius = 16
        button.addTarget(self, action: #selector(withdrawButtonTapped), for: .touchUpInside)
        return button
    }()

    // 今日可赚标签
    private lazy var dailyEarnLabel: UILabel = {
        let label = UILabel()
        label.text = "今日可赚 \(dailyEarnableCoins)金币"
        label.textColor = .white
        label.font = .systemFont(ofSize: 14)
        return label
    }()

    // 兑换比例标签
    private lazy var exchangeRateLabel: UILabel = {
        let label = UILabel()
        label.text = exchangeRate
        label.textColor = UIColor(hex: "#666666")
        label.font = .systemFont(ofSize: 14)
        label.backgroundColor = .clear
        label.textAlignment = .left
        return label
    }()

    // MARK: - 任务卡片组件

    // 发布视频任务行
    private lazy var publishVideoTaskView: TaskRowView = {
        let view = TaskRowView()
        view.configure(
            icon: UIImage(named: "gold_coin_task_publish_video_icon"),
            title: "发布视频",
            subtitle: "首次发布视频即可获得奖励",
            reward: "+100金币",
            buttonTitle: "去完成",
            isCompleted: true
        )
        view.onButtonTapped = { [weak self] in
            self?.publishVideoTaskTapped()
        }
        return view
    }()

    // 发布视频分割线
    private lazy var publishVideoSeparator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#E5E5E5")
        return view
    }()

    // 连续签到容器（包含任务行和签到日历）
    private lazy var checkInContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    // 连续签到任务行
    private lazy var checkInTaskView: TaskRowView = {
        let view = TaskRowView()
        view.configure(
            icon: UIImage(named: "gold_coin_task_checkin_icon"),
            title: "连续签到",
            subtitle: "连续签到7天可获得额外奖励",
            reward: "+100金币",
            buttonTitle: "签到",
            isCompleted: false
        )
        view.onButtonTapped = { [weak self] in
            self?.checkInTaskTapped()
        }
        return view
    }()

    // 签到日历容器
    private lazy var checkInCalendarView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    // 连续签到分割线
    private lazy var checkInSeparator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#E5E5E5")
        return view
    }()

    // 邀请好友任务行
    private lazy var inviteFriendsTaskView: TaskRowView = {
        let view = TaskRowView()
        view.configure(
            icon: UIImage(named: "gold_coin_task_invite_friends_icon"),
            title: "邀请好友",
            subtitle: "邀请1位好友注册",
            reward: "+200金币",
            buttonTitle: "去邀请",
            isCompleted: false
        )
        view.onButtonTapped = { [weak self] in
            self?.inviteFriendsTaskTapped()
        }
        return view
    }()

    // 邀请好友分割线
    private lazy var inviteFriendsSeparator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#E5E5E5")
        return view
    }()

    // 观看视频容器（包含任务行和进度条）
    private lazy var watchVideoContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    // 观看视频任务行
    private lazy var watchVideoTaskView: TaskRowView = {
        let view = TaskRowView()
        view.configure(
            icon: UIImage(named: "gold_coin_task_watch_video_icon"),
            title: "观看视频",
            subtitle: "观看视频10分钟",
            reward: "+100金币",
            buttonTitle: "去观看",
            isCompleted: false
        )
        view.onButtonTapped = { [weak self] in
            self?.watchVideoTaskTapped()
        }
        return view
    }()

    // 观看视频进度条
    private lazy var videoProgressView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    private lazy var videoProgressBackgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#E5E5E5")
        view.layer.cornerRadius = 4
        return view
    }()

    private lazy var videoProgressForegroundView: UIView = {
        let view = UIView()
        view.backgroundColor = .appThemeOrange
        view.layer.cornerRadius = 4
        return view
    }()

    private lazy var videoProgressLabel: UILabel = {
        let label = UILabel()
        label.text = "已观看3分钟，还差7分钟"
        label.textColor = UIColor(hex: "#666666")
        label.font = .systemFont(ofSize: 12)
        return label
    }()

    // 悬浮礼盒按钮（可长按拖动）
    private var giftButton: DraggableGiftButton?
    // 弹窗弱引用，避免重复创建
    private weak var currentGoldPopup: GoldCoinPopupView?
    // 日签弹窗弱引用，避免重复创建
    private weak var currentCheckInPopup: DailyAttendancePopupView?

    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupData()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        NotificationCenter.default.addObserver(self, selector: #selector(handleCountdownTick(_:)), name: .goldCountdownTick, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(handleCountdownFinished(_:)), name: .goldCountdownFinished, object: nil)
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        NotificationCenter.default.removeObserver(self, name: .goldCountdownTick, object: nil)
        NotificationCenter.default.removeObserver(self, name: .goldCountdownFinished, object: nil)
    }

    // MARK: - 签到状态注入（后续由服务器控制）
    func configureCheckIn(consecutiveDays: Int, hasClickedToday: Bool) {
        consecutiveCheckInDays = max(0, min(7, consecutiveDays))
        hasClickedCheckInButtonToday = hasClickedToday
        generateCheckInDays()
        setupCheckInCalendar()
        updateCheckInButtonState()
    }

    // 兼容旧的测试方法
    func setTestConsecutiveCheckInDays(_ days: Int) {
        configureCheckIn(consecutiveDays: days, hasClickedToday: false)
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // 初始化悬浮礼盒按钮
        setupGiftButtonIfNeeded()

        // 调试约束信息
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            print("[约束调试] checkInTaskView frame: \(self.checkInTaskView.frame)")
            print("[约束调试] checkInCalendarView frame: \(self.checkInCalendarView.frame)")
            print("[约束调试] 实际间距: \(self.checkInCalendarView.frame.minY - self.checkInTaskView.frame.maxY)")

            print("[约束调试] watchVideoTaskView frame: \(self.watchVideoTaskView.frame)")
            print("[约束调试] videoProgressView frame: \(self.videoProgressView.frame)")
            print("[约束调试] 观看视频实际间距: \(self.videoProgressView.frame.minY - self.watchVideoTaskView.frame.maxY)")
        }
    }

    // MARK: - 设置方法

    private func setupUI() {
        navTitle = "任务中心"
        contentView.backgroundColor = .white

        // 添加滚动视图
        contentView.addSubview(scrollView)
        scrollView.addSubview(scrollContentView)

        // 添加头部背景容器
        scrollContentView.addSubview(headerBackgroundView)

        // 添加当前金币背景图到头部背景容器
        headerBackgroundView.addSubview(goldCoinBackgroundView)
        goldCoinBackgroundView.addSubview(currentGoldTitleLabel)
        goldCoinBackgroundView.addSubview(goldCoinAmountLabel)
        goldCoinBackgroundView.addSubview(withdrawButton)
        goldCoinBackgroundView.addSubview(dailyEarnLabel)

        // 添加兑换比例标签到头部背景容器
        headerBackgroundView.addSubview(exchangeRateLabel)

        // 添加任务行和分割线
        scrollContentView.addSubview(publishVideoTaskView)
        scrollContentView.addSubview(publishVideoSeparator)

        // 添加连续签到容器
        scrollContentView.addSubview(checkInContainerView)
        checkInContainerView.addSubview(checkInTaskView)
        checkInContainerView.addSubview(checkInCalendarView)
        scrollContentView.addSubview(checkInSeparator)

        // 添加邀请好友任务
        scrollContentView.addSubview(inviteFriendsTaskView)
        scrollContentView.addSubview(inviteFriendsSeparator)

        // 添加观看视频容器
        scrollContentView.addSubview(watchVideoContainerView)
        watchVideoContainerView.addSubview(watchVideoTaskView)
        watchVideoContainerView.addSubview(videoProgressView)

        // 设置进度条
        videoProgressView.addSubview(videoProgressBackgroundView)
        videoProgressView.addSubview(videoProgressForegroundView)
        videoProgressView.addSubview(videoProgressLabel)

        setupConstraints()
        setupData() // 先设置数据
        setupCheckInCalendar() // 再设置UI
    }

    private func setupData() {
        // 根据当前连续签到天数生成签到数据
        generateCheckInDays()
        updateCheckInButtonState()
        updateVideoProgress()
    }

    private func generateCheckInDays() {
        checkInDays = []
        // 固定展示 第1天 ~ 第7天；连续到第N天就亮前N天
        for i in 1...7 {
            let dayName = "第\(i)天"
            let reward = i * 10 // +10, +20, ... +70
            let isCheckedIn = i <= consecutiveCheckInDays
            checkInDays.append(CheckInDay(day: dayName, reward: reward, isCheckedIn: isCheckedIn))
        }
    }

    // 更新签到按钮状态（达到7天则置为完成）
    private func updateCheckInButtonState() {
        if consecutiveCheckInDays >= 7 {
            checkInTaskView.updateButtonTitle("已完成")
            checkInTaskView.setCompleted(true)
        } else {
            checkInTaskView.updateButtonTitle("签到")
            checkInTaskView.setCompleted(false)
        }
    }

    private func setupConstraints() {
        // 滚动视图约束
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        scrollContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        // 头部背景容器约束 - 距离顶部16pt，左右各16pt，高度168pt
        headerBackgroundView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(168)
        }

        // 当前金币背景图约束 - 在头部背景容器上方
        goldCoinBackgroundView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(120) // 调整高度，为下方文案留空间
        }

        // 当前金币标题约束
        currentGoldTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.height.equalTo(20)
        }

        // 金币数量约束
        goldCoinAmountLabel.snp.makeConstraints { make in
            make.top.equalTo(currentGoldTitleLabel.snp.bottom).offset(8)
            make.left.equalTo(currentGoldTitleLabel)
        }

        // 提现按钮约束
        withdrawButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.width.equalTo(74)
            make.height.equalTo(32)
        }

        // 今日可赚标签约束
        dailyEarnLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-16)
            make.left.equalTo(currentGoldTitleLabel)
        }

        // 兑换比例标签约束 - 在当前金币背景图下方+8pt，左边距离背景View +16pt
        exchangeRateLabel.snp.makeConstraints { make in
            make.top.equalTo(goldCoinBackgroundView.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
            make.bottom.equalToSuperview().offset(-16)
        }

        // 发布视频任务行约束 - 高度88
        publishVideoTaskView.snp.makeConstraints { make in
            make.top.equalTo(headerBackgroundView.snp.bottom).offset(16)
            make.left.right.equalToSuperview()
            make.height.equalTo(88)
        }

        // 发布视频分割线约束
        publishVideoSeparator.snp.makeConstraints { make in
            make.top.equalTo(publishVideoTaskView.snp.bottom)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(1)
        }

        // 连续签到容器约束 - 移除固定高度，让内容决定高度
        checkInContainerView.snp.makeConstraints { make in
            make.top.equalTo(publishVideoSeparator.snp.bottom)
            make.left.right.equalToSuperview()
        }

        // 连续签到任务行约束
        checkInTaskView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(80)
        }

        // 签到日历约束 - 距离连续签到图标20pt
        checkInCalendarView.snp.makeConstraints { make in
            make.top.equalTo(checkInTaskView.snp.bottom).offset(1)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16) // 底部留16pt间距
        }

        print("[约束调试] 签到日历约束已设置：top offset = 20pt")

        // 连续签到分割线约束
        checkInSeparator.snp.makeConstraints { make in
            make.top.equalTo(checkInContainerView.snp.bottom)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(1)
        }

        // 邀请好友任务行约束 - 高度80
        inviteFriendsTaskView.snp.makeConstraints { make in
            make.top.equalTo(checkInSeparator.snp.bottom)
            make.left.right.equalToSuperview()
            make.height.equalTo(80)
        }

        // 邀请好友分割线约束
        inviteFriendsSeparator.snp.makeConstraints { make in
            make.top.equalTo(inviteFriendsTaskView.snp.bottom)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(1)
        }

        // 观看视频容器约束 - 移除固定高度，让内容决定高度
        watchVideoContainerView.snp.makeConstraints { make in
            make.top.equalTo(inviteFriendsSeparator.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-20)
        }

        // 观看视频任务行约束
        watchVideoTaskView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(80)
        }

        // 观看视频进度条约束
        videoProgressView.snp.makeConstraints { make in
            make.top.equalTo(watchVideoTaskView.snp.bottom).offset(1)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16) // 底部留16pt间距
        }

        // 进度条背景约束 - 移除内部间距，因为外部已经有16pt间距
        videoProgressBackgroundView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
            make.height.equalTo(8)
        }

        // 进度条前景约束
        videoProgressForegroundView.snp.makeConstraints { make in
            make.top.left.height.equalTo(videoProgressBackgroundView)
            make.width.equalTo(videoProgressBackgroundView).multipliedBy(videoWatchProgress)
        }

        // 进度标签约束
        videoProgressLabel.snp.makeConstraints { make in
            make.top.equalTo(videoProgressBackgroundView.snp.bottom).offset(8)
            make.left.equalToSuperview()
        }
    }

    private func setupCheckInCalendar() {
        // 清除之前的子视图
        checkInCalendarView.subviews.forEach { $0.removeFromSuperview() }

        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 8

        checkInCalendarView.addSubview(stackView)
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 创建签到日期视图
        for (index, checkInDay) in checkInDays.enumerated() {
            let dayView = createCheckInDayView(checkInDay: checkInDay)
            stackView.addArrangedSubview(dayView)
        }
    }

    private func createCheckInDayView(checkInDay: CheckInDay) -> UIView {
        let containerView = UIView()

        // 奖励标签 - 32*32的方块，圆角4pt
        let rewardLabel = UILabel()
        rewardLabel.text = "+\(checkInDay.reward)"
        rewardLabel.font = .systemFont(ofSize: 11, weight: .medium)
        rewardLabel.textAlignment = .center
        rewardLabel.layer.cornerRadius = 4
        rewardLabel.clipsToBounds = true

        // 根据选中状态设置颜色
        if checkInDay.isCheckedIn {
            // 选中状态：#FF9143背景色，+N的字体是#FF8F1F色
            rewardLabel.backgroundColor = UIColor(hex: "#FF9143",alpha: 0.15)
            rewardLabel.textColor = UIColor(hex: "#FF8F1F")
        } else {
            // 未选中状态：#E5E5E5背景色，#7E7E7E文字颜色
            rewardLabel.backgroundColor = UIColor(hex: "#E5E5E5")
            rewardLabel.textColor = UIColor(hex: "#7E7E7E")
        }

        // 日期标签
        let dayLabel = UILabel()
        dayLabel.text = checkInDay.day
        dayLabel.textColor = checkInDay.isCheckedIn ? .appThemeOrange : UIColor(hex: "#333333")
        dayLabel.font = .systemFont(ofSize: 11)
        dayLabel.textAlignment = .center

        containerView.addSubview(rewardLabel)
        containerView.addSubview(dayLabel)

        // 奖励标签约束 - 32*32大小
        rewardLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.width.height.equalTo(32)
        }

        // 日期标签约束
        dayLabel.snp.makeConstraints { make in
            make.top.equalTo(rewardLabel.snp.bottom).offset(6)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
        }

        return containerView
    }

    private func updateVideoProgress() {
        let progressText = "已观看\(videoWatchedSeconds / 60)分钟，还差\((videoTotalSeconds - videoWatchedSeconds) / 60)分钟"
        videoProgressLabel.text = progressText

        // 更新进度条宽度
        videoProgressForegroundView.snp.updateConstraints { make in
            make.width.equalTo(videoProgressBackgroundView).multipliedBy(videoWatchProgress)
        }
    }

    // 初始化并添加悬浮礼盒按钮
    private func setupGiftButtonIfNeeded() {
        // 已添加则不重复添加
        if let button = giftButton, button.superview != nil {
            contentView.bringSubviewToFront(button)
            return
        }

        let button = DraggableGiftButton()
        // 初始化文案
        button.setTitle(initialGiftTitle())
        button.addTarget(self, action: #selector(giftButtonTapped), for: .touchUpInside)

        contentView.addSubview(button)

        // 动态尺寸与初始位置（靠右下，留出一定边距；宽度按标题自适应）
        let defaultSize = button.intrinsicContentSize
        let height: CGFloat = defaultSize.height.isFinite ? defaultSize.height : 120
        let inset: CGFloat = 16
        let maxWidth = max(120, contentView.bounds.width - inset * 2)
        let width: CGFloat = button.preferredWidth(maxWidth: maxWidth)
        let x = contentView.bounds.width - inset - width
        let y = max(inset, contentView.bounds.height - height - 100)
        button.frame = CGRect(x: x, y: y, width: width, height: height)

        giftButton = button
        contentView.bringSubviewToFront(button)
    }

    @objc private func giftButtonTapped() {
        let mgr = GoldCountdownManager.shared
        // 达到每日上限：仅提示，不弹窗
        if mgr.isDailyLimitReached {
            showHUD("今日领取数量已达上限，请明日再来~")
            return
        }
        print("礼盒按钮被点击 -> 弹出金币弹窗")
        presentGoldCoinPopup()
    }

    // 展示金币弹窗（左右24pt，按327:436动态高度）
    private func presentGoldCoinPopup() {
        // 若已存在，先移除旧的
        currentGoldPopup?.dismiss(animated: false)

        // 达到每日上限：不弹窗，直接 HUD
        let mgr = GoldCountdownManager.shared
        if mgr.isDailyLimitReached {
            updateGiftButtonTitle("明天再来")
            showHUD("今日领取数量已达上限，请明日再来~")
            return
        }

        let popup = GoldCoinPopupView()
        popup.onDismiss = { [weak self] in
            self?.currentGoldPopup = nil
        }
        // 根据当前状态配置
        if mgr.isRunning {
            let reward = mgr.rewardCoins > 0 ? mgr.rewardCoins : defaultRewardCoins
            popup.configure(mode: .countdown, valueText: formatSeconds(mgr.remainingSeconds), subtitleText: "即可获得\(reward)金币！")
        } else {
            let reward = currentRewardCoins()
            popup.configure(mode: .reward, valueText: "\(reward)金币")
        }
        popup.onConfirm = { [weak self] in
            self?.handlePopupConfirm()
        }
        popup.show(in: self.view)
        currentGoldPopup = popup
    }

    // 对外提供：更新礼盒按钮文案
    func updateGiftButtonTitle(_ text: String) {
        guard let button = giftButton else { return }
        button.setTitle(text)
        // 标题变化后同步调整宽度，保持在父视图边界内
        let inset: CGFloat = 8
        let maxWidth = max(120, contentView.bounds.width - inset * 2)
        let newWidth = button.preferredWidth(maxWidth: maxWidth)
        var frame = button.frame
        let oldCenter = button.center
        frame.size.width = newWidth
        button.frame = frame
        // 约束中心点不越界（水平）
        let halfW = newWidth * 0.5
        let minX = inset + halfW
        let maxX = contentView.bounds.width - inset - halfW
        let clampedX = min(max(oldCenter.x, minX), maxX)
        button.center = CGPoint(x: clampedX, y: oldCenter.y)
    }

    // MARK: - Countdown Integration
    private func initialGiftTitle() -> String {
        let mgr = GoldCountdownManager.shared
        if mgr.isDailyLimitReached {
            return "明天再来"
        } else if mgr.isRunning {
            let reward = mgr.rewardCoins > 0 ? mgr.rewardCoins : defaultRewardCoins
            return "\(formatSeconds(mgr.remainingSeconds)) \(reward)金币"
        } else {
            let reward = currentRewardCoins()
            return "点击可领 \(reward)金币"
        }
    }

    private func currentRewardCoins() -> Int {
        let mgr = GoldCountdownManager.shared
        return mgr.rewardCoins > 0 ? mgr.rewardCoins : defaultRewardCoins
    }

    private func formatSeconds(_ seconds: Int) -> String {
        let m = seconds / 60
        let s = seconds % 60
        return String(format: "%02d:%02d", m, s)
    }

    @objc private func handleCountdownTick(_ noti: Notification) {
        let mgr = GoldCountdownManager.shared
        if mgr.isDailyLimitReached {
            // 达到上限后不再展示倒计时文案
            updateGiftButtonTitle("明天再来")
            return
        }
        let reward = (noti.userInfo?["reward"] as? Int) ?? currentRewardCoins()
        let remain = mgr.remainingSeconds
        // 更新礼盒
        updateGiftButtonTitle("\(formatSeconds(remain)) \(reward)金币")
        // 若弹窗存在且是倒计时态，则同步时间
        if let popup = currentGoldPopup {
            popup.configure(mode: .countdown, valueText: formatSeconds(remain), subtitleText: "即可获得\(reward)金币！")
        }
    }

    @objc private func handleCountdownFinished(_ noti: Notification) {
        let mgr = GoldCountdownManager.shared
        if mgr.isDailyLimitReached {
            updateGiftButtonTitle("明天再来")
            return
        }
        let reward = (noti.userInfo?["reward"] as? Int) ?? currentRewardCoins()
        // 更新礼盒文案为可领取
        updateGiftButtonTitle("点击可领 \(reward)金币")
        // 若弹窗还在，切换为领取态（不收起弹窗）
        if let popup = currentGoldPopup {
            popup.configure(mode: .reward, valueText: "\(reward)金币")
        }
    }

    private func handlePopupConfirm() {
        let mgr = GoldCountdownManager.shared
        if mgr.isRunning {
            // 倒计时态下点击：不做领取
            return
        }
        // 达到每日上限：仅提示
        if mgr.isDailyLimitReached {
            showHUD("今日领取数量已达上限，请明日再来~")
            return
        }
        // 领取逻辑：加金币
        let reward = currentRewardCoins()
        currentGoldCoins += reward
        goldCoinAmountLabel.text = "\(currentGoldCoins)"
        // 领取计数 +1
        let count = mgr.incrementDailyClaim()
        // 若已达上限：不再开启倒计时，按钮改为“明天再来”
        if count >= mgr.dailyLimit {
            updateGiftButtonTitle("明天再来")
        } else {
            // 开始全局30s倒计时
            mgr.start(duration: 30, reward: reward)
            // 更新礼盒为倒计时文案
            updateGiftButtonTitle("00:30 \(reward)金币")
        }
    }

    // MARK: - HUD / Toast
    private func showHUD(_ message: String, duration: TimeInterval = 2.0) {
        DispatchQueue.main.async {
            let container = UIView()
            container.backgroundColor = UIColor.black.withAlphaComponent(0.8)
            container.layer.cornerRadius = 8
            container.clipsToBounds = true
            container.alpha = 0

            let label = UILabel()
            label.textColor = .white
            label.font = .systemFont(ofSize: 14)
            label.numberOfLines = 0
            label.textAlignment = .center
            label.text = message
            label.translatesAutoresizingMaskIntoConstraints = false
            container.addSubview(label)

            NSLayoutConstraint.activate([
                label.topAnchor.constraint(equalTo: container.topAnchor, constant: 12),
                label.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -12),
                label.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 16),
                label.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -16)
            ])

            guard let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) else { return }
            window.addSubview(container)
            container.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                container.centerXAnchor.constraint(equalTo: window.centerXAnchor),
                container.centerYAnchor.constraint(equalTo: window.centerYAnchor),
                container.widthAnchor.constraint(lessThanOrEqualTo: window.widthAnchor, multiplier: 0.8)
            ])

            UIView.animate(withDuration: 0.25, animations: { container.alpha = 1 }) { _ in
                DispatchQueue.main.asyncAfter(deadline: .now() + duration) {
                    UIView.animate(withDuration: 0.25, animations: { container.alpha = 0 }) { _ in
                        container.removeFromSuperview()
                    }
                }
            }
        }
    }

    // MARK: - 事件处理

    @objc private func withdrawButtonTapped() {
        print("提现按钮被点击")
        // 跳转到提现页面
        let withdrawalVC = GoldCoinWithdrawalCenterViewController()
        navigationController?.pushViewController(withdrawalVC, animated: true)
    }

    private func publishVideoTaskTapped() {
        print("发布视频任务被点击")
        // TODO: 跳转到发布视频页面
        let alert = UIAlertController(title: "发布视频", message: "跳转到发布视频页面", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func checkInTaskTapped() {
        print("签到任务被点击")
        // 新逻辑：点击仅弹窗确认，确认后才执行签到
        presentCheckInPopup()
    }

    private func inviteFriendsTaskTapped() {
        print("邀请好友任务被点击")
        // TODO: 跳转到邀请好友页面
        let alert = UIAlertController(title: "邀请好友", message: "跳转到邀请好友页面", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func watchVideoTaskTapped() {
        print("观看视频任务被点击")
        // TODO: 跳转到视频观看页面
        let alert = UIAlertController(title: "观看视频", message: "跳转到视频观看页面", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    // 自定义签到弹窗（先实现背景与关闭按钮，内容后续补充）
    private func presentCheckInPopup() {
        // 若已存在先移除
        currentCheckInPopup?.dismiss(animated: false)

        let popup = DailyAttendancePopupView()
        // 示例文案 + 动态已连签天数
        popup.configure(
            title: "连续签到7天得18888金币",
            subtitle: "已连签\(consecutiveCheckInDays)/7天，断签将无法得到最终大奖"
        )
        popup.onDismiss = { [weak self] in
            self?.currentCheckInPopup = nil
        }
        popup.onConfirm = { [weak self, weak popup] in
            self?.performCheckIn()
            popup?.dismiss(animated: true)
        }
        popup.show(in: self.view)
        currentCheckInPopup = popup
    }

    private func performCheckIn() {
        // 检查今天是否可以签到（连续签到天数对应的下一天）
        if consecutiveCheckInDays < 7 && consecutiveCheckInDays < checkInDays.count {
            // 执行签到
            consecutiveCheckInDays += 1

            // 重新生成签到数据
            generateCheckInDays()

            // 更新UI
            setupCheckInCalendar()

            // 更新按钮状态
            updateCheckInButtonState()

            // 显示签到成功提示
            let reward = consecutiveCheckInDays * 10 // 当前签到天数对应的奖励
            currentGoldCoins += reward
            goldCoinAmountLabel.text = "\(currentGoldCoins)"

            let alert = UIAlertController(title: "签到成功", message: "获得\(reward)金币奖励！连续签到\(consecutiveCheckInDays)天", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
        } else {
            let alert = UIAlertController(title: "提示", message: "已完成7天连续签到", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
        }
    }
}

// MARK: - 数据模型

struct CheckInDay {
    let day: String
    let reward: Int
    var isCheckedIn: Bool
}

// MARK: - 任务行组件（无卡片样式）

class TaskRowView: UIView {

    // MARK: - UI 组件
    private lazy var iconImageView: UIImageView = {
        let imageView = UIImageView()
//        imageView.contentMode = .scaleAspectFit
//        imageView.backgroundColor = UIColor(hex: "#F0F0F0")
//        imageView.layer.cornerRadius = 20
//        imageView.clipsToBounds = true
        return imageView
    }()

    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(hex: "#333333")
        label.font = .systemFont(ofSize: 16, weight: .medium)
        return label
    }()

    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.textColor = UIColor(hex: "#000000",alpha: 0.45)
        label.font = .systemFont(ofSize: 12)
        return label
    }()

    private lazy var rewardLabel: UILabel = {
        let label = UILabel()
        label.textColor = .appThemeOrange
        label.font = .systemFont(ofSize: 14, weight: .medium)
        return label
    }()

    private lazy var actionButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 12, weight: .medium)
        button.backgroundColor = .appThemeOrange
        button.layer.cornerRadius = 4
        button.addTarget(self, action: #selector(actionButtonTapped), for: .touchUpInside)
        return button
    }()

    // MARK: - 属性
    var onButtonTapped: (() -> Void)?
    private var isCompleted: Bool = false

    // MARK: - 初始化
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        backgroundColor = .white

        addSubview(iconImageView)
        addSubview(titleLabel)
        addSubview(subtitleLabel)
        addSubview(rewardLabel)
        addSubview(actionButton)

        setupConstraints()
    }

    private func setupConstraints() {
        iconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(40)
        }

        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(12)
            make.top.equalTo(iconImageView.snp.top).offset(-4)
            make.height.equalTo(23)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(6)
            make.height.equalTo(17)
            // 移除底部间距，让容器自己控制间距
        }

        actionButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalTo(subtitleLabel) // 按钮对齐副标题
            make.width.equalTo(60)
            make.height.equalTo(23)
        }

        rewardLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalTo(titleLabel) // +金币数对齐标题
        }
    }

    // MARK: - 公共方法

    func configure(icon: UIImage?, title: String, subtitle: String, reward: String, buttonTitle: String, isCompleted: Bool) {
        iconImageView.image = icon
        titleLabel.text = title
        subtitleLabel.text = subtitle
        rewardLabel.text = reward
        actionButton.setTitle(buttonTitle, for: .normal)
        setCompleted(isCompleted)
    }

    func setCompleted(_ completed: Bool) {
        isCompleted = completed
        if completed {
            actionButton.backgroundColor = UIColor(hex: "#CCCCCC")
            actionButton.setTitle("已完成", for: .normal)
            actionButton.isEnabled = false
        } else {
            actionButton.backgroundColor = .appThemeOrange
            actionButton.isEnabled = true
        }
    }

    func updateButtonTitle(_ title: String) {
        actionButton.setTitle(title, for: .normal)
    }

    @objc private func actionButtonTapped() {
        if !isCompleted {
            onButtonTapped?()
        }
    }
}

// MARK: - 悬浮礼盒按钮（可长按拖动）
class DraggableGiftButton: UIControl {
    // 背景包含“礼盒 + 橙色条”的整图
    private let backgroundImageView: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(named: "gold_coin_gift_package")
        iv.contentMode = .scaleAspectFit
        iv.isUserInteractionEnabled = false
        return iv
    }()

    // 仅管理橙色区域的文案
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.textColor = .white
        label.font = .systemFont(ofSize: 10, weight: .medium)
        label.textAlignment = .center
        label.numberOfLines = 1
        label.adjustsFontSizeToFitWidth = true
        label.minimumScaleFactor = 0.8
        return label
    }()

    // 拖拽
    private var startTouchPointInSelf: CGPoint = .zero
    private var didDrag: Bool = false
    private let dragEdgeInset: CGFloat = 8
    var edgeSnapEnabled: Bool = true

    override init(frame: CGRect) {
        super.init(frame: frame)
        setup()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setup()
    }

    private func setup() {
        clipsToBounds = false
        isUserInteractionEnabled = true
        isExclusiveTouch = false

        addSubview(backgroundImageView)
        addSubview(titleLabel)

        // 长按开始拖动
        let longPress = UILongPressGestureRecognizer(target: self, action: #selector(handleLongPress(_:)))
        longPress.minimumPressDuration = 0.25
        longPress.cancelsTouchesInView = true
        addGestureRecognizer(longPress)

        // 轻点触发点击事件
        let tap = UITapGestureRecognizer(target: self, action: #selector(handleTap))
        addGestureRecognizer(tap)
    }

    // 文案设置（作用于橙色区域）
    func setTitle(_ title: String) {
        titleLabel.text = title
        setNeedsLayout()
    }

    // 按标题动态计算合适的宽度（包含橙色条左右内边距）
    // maxWidth 由外部限制，避免过宽溢出屏幕
    func preferredWidth(maxWidth: CGFloat) -> CGFloat {
        let text = titleLabel.text ?? ""
        let font = titleLabel.font ?? .systemFont(ofSize: 10, weight: .medium)
        // 文本单行宽度估算
        let bounding = (text as NSString).boundingRect(
            with: CGSize(width: CGFloat.greatestFiniteMagnitude, height: 28),
            options: [.usesLineFragmentOrigin, .usesFontLeading],
            attributes: [.font: font],
            context: nil
        )
        // 与 layoutSubviews 中保持一致的左右内边距
        let horizontalInsets: CGFloat = 14 + 14
        // 为文本增加少量余量，避免挤压
        let padding: CGFloat = 10
        let wanted = bounding.width + horizontalInsets + padding
        // 设定一个适度的最小宽度，避免图片过度压缩
        let minWidth: CGFloat = 120
        // 若有原始图片尺寸，尽量不超过其 1.6 倍，避免过度拉伸
        let intrinsicW = intrinsicContentSize.width
        let safeMax = intrinsicW.isFinite && intrinsicW > 0 ? min(maxWidth, intrinsicW * 1.6) : maxWidth
        return max(minWidth, min(wanted, safeMax))
    }

    override var intrinsicContentSize: CGSize {
        if let size = backgroundImageView.image?.size, size.width > 0, size.height > 0 {
            return size
        }
        return CGSize(width: 156, height: 120)
    }

    override func sizeThatFits(_ size: CGSize) -> CGSize {
        return intrinsicContentSize
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        backgroundImageView.frame = bounds

        // 将文案放到背景图底部的橙色区域
        let barHeight = max(32, bounds.height * 0.26)
        let insets = UIEdgeInsets(top: 0, left: 14, bottom: 10, right: 14)
        let labelY = bounds.height - barHeight
        let labelFrame = CGRect(x: insets.left,
                                y: labelY + (barHeight - 28) * 0.5 + 3,
                                width: bounds.width - insets.left - insets.right,
                                height: 28)
        titleLabel.frame = labelFrame
    }

    // MARK: - 拖拽手势
    @objc private func handleLongPress(_ gesture: UILongPressGestureRecognizer) {
        guard let superview = self.superview else { return }
        switch gesture.state {
        case .began:
            didDrag = false
            startTouchPointInSelf = gesture.location(in: self)
            animate(scale: 1.03, alpha: 1)
        case .changed:
            let locationInSuper = gesture.location(in: superview)
            var newCenter = CGPoint(x: locationInSuper.x - startTouchPointInSelf.x + bounds.width * 0.5,
                                    y: locationInSuper.y - startTouchPointInSelf.y + bounds.height * 0.5)
            let halfW = bounds.width * 0.5
            let halfH = bounds.height * 0.5
            let minX = dragEdgeInset + halfW
            let maxX = superview.bounds.width - dragEdgeInset - halfW
            let minY = dragEdgeInset + halfH
            let maxY = superview.bounds.height - dragEdgeInset - halfH
            newCenter.x = max(minX, min(maxX, newCenter.x))
            newCenter.y = max(minY, min(maxY, newCenter.y))
            if distance(from: center, to: newCenter) > 0.5 { didDrag = true }
            center = newCenter
        case .ended, .cancelled, .failed:
            if edgeSnapEnabled, let superview = self.superview {
                let leftX = dragEdgeInset + bounds.width * 0.5
                let rightX = superview.bounds.width - dragEdgeInset - bounds.width * 0.5
                let targetX = (center.x < superview.bounds.midX) ? leftX : rightX
                UIView.animate(withDuration: 0.2, delay: 0, options: [.curveEaseOut]) {
                    self.center.x = targetX
                }
            }
            animate(scale: 1.0, alpha: 1)
        default:
            break
        }
    }

    @objc private func handleTap() {
        // 触发 UIControl 的 .touchUpInside 事件
        sendActions(for: .touchUpInside)
    }

    private func distance(from: CGPoint, to: CGPoint) -> CGFloat {
        let dx = from.x - to.x
        let dy = from.y - to.y
        return sqrt(dx*dx + dy*dy)
    }

    private func animate(scale: CGFloat, alpha: CGFloat) {
        UIView.animate(withDuration: 0.15) {
            self.transform = CGAffineTransform(scaleX: scale, y: scale)
            self.alpha = alpha
        }
    }
}

// MARK: - 金币弹窗视图（左右24pt，按327:436动态高度）
class GoldCoinPopupView: UIView {
    enum Mode { case countdown, reward }
    // 遮罩
    private let dimView: UIControl = {
        let v = UIControl()
        v.backgroundColor = UIColor.black.withAlphaComponent(0.6)
        return v
    }()

    // 背景卡片（切图）
    private let contentImageView: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(named: "countdown_window_obtaining_gold_coins")
        iv.contentMode = .scaleAspectFit
        iv.isUserInteractionEnabled = true
        return iv
    }()

    // 叠加容器：与 contentImageView 同尺寸，用于按比例放置内部控件
    let contentOverlayView: UIView = {
        let v = UIView()
        v.backgroundColor = .clear
        return v
    }()

    // 文案：标题（“倒计时结束后/恭喜获得”）
    private let titleLabel: UILabel = {
        let lb = UILabel()
        lb.textAlignment = .center
        lb.textColor = UIColor(hex: "#000000", alpha: 0.85)
        lb.numberOfLines = 1
        return lb
    }()

    // 文案：大号数值（倒计时/金币）
    private let valueLabel: UILabel = {
        let lb = UILabel()
        lb.textAlignment = .center
        lb.textColor = UIColor(hex: "#FF6666")
        lb.numberOfLines = 1
        return lb
    }()

    // 比例占位视图（用于把 top/间距/高度按 436 设计高度折算）
    private let topSpacer = UIView()
    private let gapSpacer = UIView()
    private let bottomSpacer = UIView()
    

    private var mode: Mode = .countdown
    private let subtitleLabel: UILabel = {
        let lb = UILabel()
        lb.textAlignment = .center
        lb.textColor = UIColor(hex: "#FF8F1F")
        lb.numberOfLines = 1
        return lb
    }()
    private let subtitleGap4 = UIView() // 标题与副标题 4pt 间距

    // 确认按钮
    private let confirmButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setTitle("确认", for: .normal)
        btn.setTitleColor(.white, for: .normal)
        btn.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        btn.backgroundColor = UIColor(hex: "#FF8F1F")
        btn.layer.cornerRadius = 21
        btn.clipsToBounds = true
        return btn
    }()
    private var confirmBottomConstraint: Constraint?
    var onConfirm: (() -> Void)?

    // 关闭按钮
    private let closeButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "countdown_window_obtaining_gold_coins_closed_btn"), for: .normal)
        return btn
    }()

    // 对外回调
    var onDismiss: (() -> Void)?

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        backgroundColor = .clear

        addSubview(dimView)
        addSubview(contentImageView)
        addSubview(closeButton)

        dimView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 内容卡片：左右各24pt，按宽高比 327:436 计算高度（整体上移24pt）
        contentImageView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(24)
            make.centerY.equalToSuperview().offset(-24)
            make.height.equalTo(contentImageView.snp.width).multipliedBy(436.0/327.0)
        }

        // 叠加容器与卡片同大小
        contentImageView.addSubview(contentOverlayView)
        contentOverlayView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        setupOverlayContents()
        // 确认按钮放在内容区域底部，底部间距按比例缩放
        contentOverlayView.addSubview(confirmButton)
        confirmButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.width.equalTo(262)
            make.height.equalTo(43)
            confirmBottomConstraint = make.bottom.equalToSuperview().offset(-36).constraint
        }
        confirmButton.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)
        // 让 bottomSpacer 的底部贴到按钮顶部，从而使 valueLabel 在上下内容间居中
        bottomSpacer.snp.makeConstraints { make in
            make.bottom.equalTo(confirmButton.snp.top)
        }

        // 关闭按钮居中，位于卡片下方
        let closeSize: CGFloat = (closeButton.image(for: .normal)?.size.width ?? 28)
        closeButton.snp.makeConstraints { make in
            make.top.equalTo(contentImageView.snp.bottom).offset(16)
            make.centerX.equalTo(contentImageView.snp.centerX)
            make.width.height.equalTo(closeSize)
            make.bottom.lessThanOrEqualTo(self.safeAreaLayoutGuide.snp.bottom).offset(-20)
        }

        dimView.addTarget(self, action: #selector(handleBackgroundTap), for: .touchUpInside)
        closeButton.addTarget(self, action: #selector(closeTapped), for: .touchUpInside)
    }

    // MARK: - Public
    func show(in parent: UIView) {
        parent.addSubview(self)
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 动画
        alpha = 0
        contentImageView.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        UIView.animate(withDuration: 0.22, delay: 0, options: [.curveEaseOut]) {
            self.alpha = 1
            self.contentImageView.transform = .identity
        }
    }

    func dismiss(animated: Bool = true) {
        let animations = {
            self.alpha = 0
            self.contentImageView.transform = CGAffineTransform(scaleX: 0.92, y: 0.92)
        }
        let completion: (Bool) -> Void = { _ in
            self.removeFromSuperview()
            self.onDismiss?()
        }
        if animated {
            UIView.animate(withDuration: 0.18, animations: animations, completion: completion)
        } else {
            animations()
            completion(true)
        }
    }

    // 配置文案
    func configure(title: String, subtitle: String) {
        titleLabel.text = title
        subtitleLabel.text = subtitle
        adjustFontsForScale()
    }

    // 固定字号，已与设计匹配；无需按比例缩放
    private func adjustFontsForScale() {
        titleLabel.font = .systemFont(ofSize: 24, weight: .medium)
        subtitleLabel.font = .systemFont(ofSize: 16)
        valueLabel.font = .systemFont(ofSize: 48, weight: .semibold)
    }

    // MARK: - Actions
    @objc private func handleBackgroundTap() {
        dismiss(animated: true)
    }

    @objc private func closeTapped() {
        dismiss(animated: true)
    }
    
    @objc private func confirmTapped() {
        onConfirm?()
        dismiss(animated: true)
    }

    // MARK: - Overlay contents and layout
    private func setupOverlayContents() {
        // 使用垂直排列：topSpacer(比例) -> title(35) -> subtitleGap(4) -> subtitle(17) -> gap(40) -> value(58) -> bottom(自适应)
        let stack = UIStackView(arrangedSubviews: [topSpacer, titleLabel, subtitleGap4, subtitleLabel, gapSpacer, valueLabel, bottomSpacer])
        stack.axis = .vertical
        stack.alignment = .fill
        stack.distribution = .fill
        stack.spacing = 0
        contentOverlayView.addSubview(stack)
        stack.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
        }

        // 比例高度约束
        topSpacer.snp.makeConstraints { make in
            make.height.equalTo(contentOverlayView.snp.height).multipliedBy(169.0/436.0)
        }
        titleLabel.snp.makeConstraints { make in
            make.height.equalTo(35)
        }
        subtitleGap4.snp.makeConstraints { make in
            make.height.equalTo(4)
        }
        subtitleLabel.snp.makeConstraints { make in
            make.height.equalTo(17)
        }
        gapSpacer.snp.makeConstraints { make in
            make.height.equalTo(bottomSpacer.snp.height)
        }
        valueLabel.snp.makeConstraints { make in
            make.height.equalTo(58)
        }
        // bottomSpacer 高度由剩余空间决定，无需固定比例，但可给最小高度保证
        bottomSpacer.setContentHuggingPriority(.defaultLow, for: .vertical)
        bottomSpacer.setContentCompressionResistancePriority(.defaultLow, for: .vertical)

        // 默认文案
        titleLabel.font = .systemFont(ofSize: 24, weight: .medium)
        valueLabel.font = .systemFont(ofSize: 48, weight: .semibold)
        subtitleLabel.font = .systemFont(ofSize: 16)
        configure(mode: .countdown, valueText: "00:00:30", subtitleText: "即可获得200金币！")
    }

    override func layoutSubviews() {
        super.layoutSubviews()
        // 按需刷新一次，保持按钮底部固定 36pt
        confirmBottomConstraint?.update(offset: -36.0)
    }

    // MARK: - Public configure
    func configure(mode: Mode, valueText: String, subtitleText: String? = nil) {
        self.mode = mode
        switch mode {
        case .countdown:
            titleLabel.text = "倒计时结束后"
            subtitleLabel.text = subtitleText
            let hideSubtitle = (subtitleText?.isEmpty ?? true)
            subtitleLabel.isHidden = hideSubtitle
            subtitleGap4.isHidden = hideSubtitle
            confirmButton.setTitle("确认", for: .normal)
        case .reward:
            titleLabel.text = "恭喜获得"
            subtitleLabel.text = nil
            subtitleLabel.isHidden = true
            subtitleGap4.isHidden = true
            confirmButton.setTitle("开心收下", for: .normal)
        }
        valueLabel.text = valueText
        setNeedsLayout()
    }
}

// MARK: - 日签弹窗（337:446 比例，左右各 19pt）
class DailyAttendancePopupView: UIView {
    // 遮罩
    private let dimView: UIControl = {
        let v = UIControl()
        v.backgroundColor = UIColor.black.withAlphaComponent(0.6)
        return v
    }()

    // 背景图（按 337:446 比例缩放，不变形）
    private let contentImageView: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(named: "gold_coin_‌daily_attendance‌_bg")
        iv.contentMode = .scaleAspectFit
        iv.isUserInteractionEnabled = true
        return iv
    }()

    // 关闭按钮（位于弹窗上方 14pt，屏幕右侧 -14pt，29x29）
    private let closeButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(named: "gold_coin_‌daily_attendance‌_cancel"), for: .normal)
        return btn
    }()

    // 基于 337 宽度的等比缩放系数
    private var scaleFactor: CGFloat = 1.0

    // 标题
    private let titleLabel: UILabel = {
        let lb = UILabel()
        lb.textColor = UIColor(hex: "#502202")
        lb.textAlignment = .center
        lb.numberOfLines = 1
        lb.font = .systemFont(ofSize: 23, weight: .semibold)
        return lb
    }()

    // 副标题
    private let subtitleLabel: UILabel = {
        let lb = UILabel()
        lb.textColor = UIColor(hex: "#BD7611")
        lb.textAlignment = .center
        lb.numberOfLines = 1
        lb.font = .systemFont(ofSize: 11)
        return lb
    }()

    // 内容标题：“今日签到可领”（#FF8400，12pt，高36pt，居中）
    private let contentTitleLabel: UILabel = {
        let lb = UILabel()
        lb.text = "今日签到可领"
        lb.textColor = UIColor(hex: "#FF8400")
        lb.textAlignment = .center
        lb.numberOfLines = 1
        lb.font = .systemFont(ofSize: 12, weight: .medium)
        return lb
    }()

    // 顶部图标 gold_coin_‌daily_attendance‌_cell_ordinary（58x31）
    private let topIconImageView: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(named: "gold_coin_‌daily_attendance‌_cell_ordinary")
        iv.contentMode = .scaleAspectFit
        return iv
    }()

    // 金币展示标签（高度43，颜色#FF8400，数字31pt，单位12pt）
    private let coinAmountLabel: UILabel = {
        let lb = UILabel()
        lb.textAlignment = .center
        lb.textColor = UIColor(hex: "#FF8400")
        lb.numberOfLines = 1
        return lb
    }()
    private var todayCoinAmount: Int = 6666

    // 主内容图
    private let mainContentImageView: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(named: "gold_coin_‌daily_attendance‌_content")
        iv.contentMode = .scaleAspectFit
        return iv
    }()

    // 立即签到按钮（资源自带文案）
    private let confirmButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setBackgroundImage(UIImage(named: "gold_coin_‌daily_attendance‌_ok_btn"), for: .normal)
        return btn
    }()

    // 对外回调
    var onDismiss: (() -> Void)?

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }

    private func setupUI() {
        backgroundColor = .clear

        addSubview(dimView)
        addSubview(contentImageView)
        addSubview(closeButton)

        dimView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 内容卡片：左右各19pt，高度 = 宽度 * (446/337)，垂直居中
        contentImageView.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(19)
            make.centerY.equalToSuperview()
            make.height.equalTo(contentImageView.snp.width).multipliedBy(446.0/337.0)
        }

        // 关闭按钮：右侧距屏幕 14pt，按钮底部到卡片顶部 14pt，尺寸 29x29
        closeButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-14)
            make.width.height.equalTo(29)
            make.bottom.equalTo(contentImageView.snp.top).offset(-14)
        }

        // 计算等比缩放系数（以 337 为基准）
        let contentWidth = UIScreen.main.bounds.width - 38
        scaleFactor = contentWidth / 337.0

        // 添加内容视图到卡片内
        contentImageView.addSubview(titleLabel)
        contentImageView.addSubview(subtitleLabel)
        contentImageView.addSubview(contentTitleLabel)
        contentImageView.addSubview(topIconImageView)
        contentImageView.addSubview(coinAmountLabel)
        contentImageView.addSubview(mainContentImageView)
        contentImageView.addSubview(confirmButton)

        // 标题：顶部 29s，高 32s
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(29.0 * scaleFactor)
            make.centerX.equalToSuperview()
            make.height.equalTo(32.0 * scaleFactor)
            make.left.greaterThanOrEqualToSuperview().offset(16)
            make.right.lessThanOrEqualToSuperview().offset(-16)
        }

        // 副标题：紧贴标题下方，高 15s
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom)
            make.centerX.equalToSuperview()
            make.height.equalTo(15.0 * scaleFactor)
            make.left.greaterThanOrEqualToSuperview().offset(16)
            make.right.lessThanOrEqualToSuperview().offset(-16)
        }

        // 内容标题：高36s
        contentTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom)
            make.centerX.equalToSuperview()
            make.height.equalTo(36.0 * scaleFactor)
            make.left.greaterThanOrEqualToSuperview().offset(16)
            make.right.lessThanOrEqualToSuperview().offset(-16)
        }

        // 顶部图标：距内容标题 23s，58x31（乘 s）
        topIconImageView.snp.makeConstraints { make in
            make.top.equalTo(contentTitleLabel.snp.bottom).offset(23.0 * scaleFactor)
            make.centerX.equalToSuperview()
            make.width.equalTo(58.0 * scaleFactor)
            make.height.equalTo(31.0 * scaleFactor)
        }

        // 金币展示：贴紧图标底部，高 43s
        coinAmountLabel.snp.makeConstraints { make in
            make.top.equalTo(topIconImageView.snp.bottom) // 贴紧
            make.centerX.equalToSuperview()
            make.height.equalTo(43.0 * scaleFactor)
            make.left.greaterThanOrEqualToSuperview().offset(16)
            make.right.lessThanOrEqualToSuperview().offset(-16)
        }

        // 主内容图：改为距金币展示 20s，尺寸 308x231（乘 s）
        mainContentImageView.snp.makeConstraints { make in
            make.top.equalTo(coinAmountLabel.snp.bottom).offset(20.0 * scaleFactor)
            make.centerX.equalToSuperview()
            make.width.equalTo(308.0 * scaleFactor)
            make.height.equalTo(231.0 * scaleFactor)
        }

        // 立即签到按钮：距主内容图 31s，尺寸 183x52（乘 s）
        confirmButton.snp.makeConstraints { make in
            make.top.equalTo(mainContentImageView.snp.bottom).offset(31.0 * scaleFactor)
            make.centerX.equalToSuperview()
            make.width.equalTo(183.0 * scaleFactor)
            make.height.equalTo(52.0 * scaleFactor)
        }

        dimView.addTarget(self, action: #selector(handleBackgroundTap), for: .touchUpInside)
        closeButton.addTarget(self, action: #selector(closeTapped), for: .touchUpInside)
        confirmButton.addTarget(self, action: #selector(confirmTapped), for: .touchUpInside)

        // 初次根据高度调整字体
        adjustFontsForScale()
        // 设置默认金币展示
        updateCoinAmountLabel()
    }

    // MARK: - Public
    /// 配置标题与副标题
    func configure(title: String, subtitle: String) {
        titleLabel.text = title
        subtitleLabel.text = subtitle
        adjustFontsForScale()
    }

    /// 根据缩放系数微调字体：在小屏时略微缩小，正常及大屏保持设计字号
    private func adjustFontsForScale() {
        // 设计基准字号
        let baseTitle: CGFloat = 23.0
        let baseSubtitle: CGFloat = 11.0
        let baseContentTitle: CGFloat = 12.0
        // 仅在 scaleFactor < 1 时按比例缩小，且给出最小保护
        let titleSize = scaleFactor < 1.0 ? max(12.0, baseTitle * scaleFactor) : baseTitle
        let subtitleSize = scaleFactor < 1.0 ? max(9.0, baseSubtitle * scaleFactor) : baseSubtitle
        titleLabel.font = .systemFont(ofSize: titleSize, weight: .semibold)
        subtitleLabel.font = .systemFont(ofSize: subtitleSize)
        let contentTitleSize = scaleFactor < 1.0 ? max(10.0, baseContentTitle * scaleFactor) : baseContentTitle
        contentTitleLabel.font = .systemFont(ofSize: contentTitleSize, weight: .medium)
    }

    /// 对外设置今日金币数量
    func setCoinAmount(_ amount: Int) {
        todayCoinAmount = max(0, amount)
        updateCoinAmountLabel()
    }

    private func updateCoinAmountLabel() {
        // 数字 31pt，单位 12pt；颜色统一 #FF8400，高度 43s
        let numberText = "\(todayCoinAmount)"
        let unitText = "金币"
        let full = numberText + unitText
        let attr = NSMutableAttributedString(string: full)
        let color = UIColor(hex: "#FF8400")
        let scale = min(1.0, scaleFactor)
        let numberFont = UIFont.systemFont(ofSize: 31.0 * scale, weight: .semibold)
        let unitFont = UIFont.systemFont(ofSize: 12.0 * scale)
        attr.addAttributes([.foregroundColor: color, .font: numberFont], range: NSRange(location: 0, length: numberText.count))
        attr.addAttributes([.foregroundColor: color, .font: unitFont], range: NSRange(location: numberText.count, length: unitText.count))
        coinAmountLabel.attributedText = attr
    }

    func show(in parent: UIView) {
        parent.addSubview(self)
        self.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 动画
        alpha = 0
        contentImageView.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        UIView.animate(withDuration: 0.22, delay: 0, options: [.curveEaseOut]) {
            self.alpha = 1
            self.contentImageView.transform = .identity
        }
    }

    func dismiss(animated: Bool = true) {
        let animations = {
            self.alpha = 0
            self.contentImageView.transform = CGAffineTransform(scaleX: 0.92, y: 0.92)
        }
        let completion: (Bool) -> Void = { _ in
            self.removeFromSuperview()
            self.onDismiss?()
        }
        if animated {
            UIView.animate(withDuration: 0.18, animations: animations, completion: completion)
        } else {
            animations()
            completion(true)
        }
    }

    // MARK: - Actions
    @objc private func handleBackgroundTap() {
        dismiss(animated: true)
    }

    @objc private func closeTapped() {
        dismiss(animated: true)
    }

    // 供后续接入确认签到逻辑
    var onConfirm: (() -> Void)?
    @objc private func confirmTapped() {
        onConfirm?()
    }
}
